LOAD:0000000000BEB9AC luaL_openlibs                           ; DATA XREF: LOAD:000000000266E558↓o
LOAD:0000000000BEB9AC
LOAD:0000000000BEB9AC var_10          = -0x10
LOAD:0000000000BEB9AC var_8           = -8
LOAD:0000000000BEB9AC
LOAD:0000000000BEB9AC                 STP             X19, X30, [SP,#var_10]!
LOAD:0000000000BEB9B0                 ADRP            X1, #qword_1523D10@PAGE
LOAD:0000000000BEB9B4                 LDR             X1, [X1,#qword_1523D10@PAGEOFF]
LOAD:0000000000BEB9B8                 MOV             W2, WZR
LOAD:0000000000BEB9BC                 MOV             X19, X0
LOAD:0000000000BEB9C0                 BL              sub_626220
LOAD:0000000000BEB9C4                 ADRL            X1, byte_12181BE
LOAD:0000000000BEB9CC                 MOV             X0, X19
LOAD:0000000000BEB9D0                 BL              sub_631C20
LOAD:0000000000BEB9D4                 MOV             W1, #1
LOAD:0000000000BEB9D8                 MOV             X0, X19
LOAD:0000000000BEB9DC                 MOV             W2, WZR
LOAD:0000000000BEB9E0                 BL              sub_62DD80
LOAD:0000000000BEB9E4                 ADRP            X1, #qword_15267D0@PAGE
LOAD:0000000000BEB9E8                 LDR             X1, [X1,#qword_15267D0@PAGEOFF]
LOAD:0000000000BEB9EC                 MOV             X0, X19
LOAD:0000000000BEB9F0                 MOV             W2, WZR
LOAD:0000000000BEB9F4                 BL              sub_626220
LOAD:0000000000BEB9F8                 ADRL            X1, (aTd3guopackage_0+6) ; "package"
LOAD:0000000000BEBA00                 MOV             X0, X19
LOAD:0000000000BEBA04                 BL              sub_631C20
LOAD:0000000000BEBA08                 MOV             W1, #1
LOAD:0000000000BEBA0C                 MOV             X0, X19
LOAD:0000000000BEBA10                 MOV             W2, WZR
LOAD:0000000000BEBA14                 BL              sub_62DD80
LOAD:0000000000BEBA18                 ADRP            X1, #qword_1527588@PAGE
LOAD:0000000000BEBA1C                 LDR             X1, [X1,#qword_1527588@PAGEOFF]
LOAD:0000000000BEBA20                 MOV             X0, X19
LOAD:0000000000BEBA24                 MOV             W2, WZR
LOAD:0000000000BEBA28                 BL              sub_626220
LOAD:0000000000BEBA2C                 ADRL            X1, (aPackagePreload+0x1C) ; "table"
LOAD:0000000000BEBA34                 MOV             X0, X19
LOAD:0000000000BEBA38                 BL              sub_631C20
LOAD:0000000000BEBA3C                 MOV             W1, #1
LOAD:0000000000BEBA40                 MOV             X0, X19
LOAD:0000000000BEBA44                 MOV             W2, WZR
LOAD:0000000000BEBA48                 BL              sub_62DD80
LOAD:0000000000BEBA4C                 ADRP            X1, #qword_152AFB0@PAGE
LOAD:0000000000BEBA50                 LDR             X1, [X1,#qword_152AFB0@PAGEOFF]
LOAD:0000000000BEBA54                 MOV             X0, X19
LOAD:0000000000BEBA58                 MOV             W2, WZR
LOAD:0000000000BEBA5C                 BL              sub_626220
LOAD:0000000000BEBA60                 ADRL            X1, (aCopyrightC2003+0x18) ; "io"
LOAD:0000000000BEBA68                 MOV             X0, X19
LOAD:0000000000BEBA6C                 BL              sub_631C20
LOAD:0000000000BEBA70                 MOV             W1, #1
LOAD:0000000000BEBA74                 MOV             X0, X19
LOAD:0000000000BEBA78                 MOV             W2, WZR
LOAD:0000000000BEBA7C                 BL              sub_62DD80
LOAD:0000000000BEBA80                 ADRP            X1, #qword_152E970@PAGE
LOAD:0000000000BEBA84                 LDR             X1, [X1,#qword_152E970@PAGEOFF]
LOAD:0000000000BEBA88                 MOV             X0, X19
LOAD:0000000000BEBA8C                 MOV             W2, WZR
LOAD:0000000000BEBA90                 BL              sub_626220
LOAD:0000000000BEBA94                 ADRL            X1, (aOurceinnerangl+0x10) ; "os"
LOAD:0000000000BEBA9C                 MOV             X0, X19
LOAD:0000000000BEBAA0                 BL              sub_631C20
LOAD:0000000000BEBAA4                 MOV             W1, #1
LOAD:0000000000BEBAA8                 MOV             X0, X19
LOAD:0000000000BEBAAC                 MOV             W2, WZR
LOAD:0000000000BEBAB0                 BL              sub_62DD80
LOAD:0000000000BEBAB4                 ADRP            X1, #qword_1528238@PAGE
LOAD:0000000000BEBAB8                 LDR             X1, [X1,#qword_1528238@PAGEOFF]
LOAD:0000000000BEBABC                 MOV             X0, X19
LOAD:0000000000BEBAC0                 MOV             W2, WZR
LOAD:0000000000BEBAC4                 BL              sub_626220
LOAD:0000000000BEBAC8                 ADRL            X1, (aNestedAsn1Stri+0xC) ; "string"
LOAD:0000000000BEBAD0                 MOV             X0, X19
LOAD:0000000000BEBAD4                 BL              sub_631C20
LOAD:0000000000BEBAD8                 MOV             W1, #1
LOAD:0000000000BEBADC                 MOV             X0, X19
LOAD:0000000000BEBAE0                 MOV             W2, WZR
LOAD:0000000000BEBAE4                 BL              sub_62DD80
LOAD:0000000000BEBAE8                 ADRP            X1, #qword_152FD98@PAGE
LOAD:0000000000BEBAEC                 LDR             X1, [X1,#qword_152FD98@PAGEOFF]
LOAD:0000000000BEBAF0                 MOV             X0, X19
LOAD:0000000000BEBAF4                 MOV             W2, WZR
LOAD:0000000000BEBAF8                 BL              sub_626220
LOAD:0000000000BEBAFC                 ADRL            X1, aMath ; "math"
LOAD:0000000000BEBB04                 MOV             X0, X19
LOAD:0000000000BEBB08                 BL              sub_631C20
LOAD:0000000000BEBB0C                 MOV             W1, #1
LOAD:0000000000BEBB10                 MOV             X0, X19
LOAD:0000000000BEBB14                 MOV             W2, WZR
LOAD:0000000000BEBB18                 BL              sub_62DD80
LOAD:0000000000BEBB1C                 ADRP            X1, #qword_152C2D0@PAGE
LOAD:0000000000BEBB20                 LDR             X1, [X1,#qword_152C2D0@PAGEOFF]
LOAD:0000000000BEBB24                 MOV             X0, X19
LOAD:0000000000BEBB28                 MOV             W2, WZR
LOAD:0000000000BEBB2C                 BL              sub_626220
LOAD:0000000000BEBB30                 ADRL            X1, (aCcCcbanimation_35+0x17) ; "debug"
LOAD:0000000000BEBB38                 MOV             X0, X19
LOAD:0000000000BEBB3C                 BL              sub_631C20
LOAD:0000000000BEBB40                 MOV             W1, #1
LOAD:0000000000BEBB44                 MOV             X0, X19
LOAD:0000000000BEBB48                 MOV             W2, WZR
LOAD:0000000000BEBB4C                 BL              sub_62DD80
LOAD:0000000000BEBB50                 ADRP            X1, #qword_152B348@PAGE
LOAD:0000000000BEBB54                 LDR             X1, [X1,#qword_152B348@PAGEOFF]
LOAD:0000000000BEBB58                 MOV             X0, X19
LOAD:0000000000BEBB5C                 MOV             W2, WZR
LOAD:0000000000BEBB60                 BL              sub_626220
LOAD:0000000000BEBB64                 ADRL            X1, (aHmacGost341120_0+0x19) ; "bit"
LOAD:0000000000BEBB6C                 MOV             X0, X19
LOAD:0000000000BEBB70                 BL              sub_631C20
LOAD:0000000000BEBB74                 MOV             W1, #1
LOAD:0000000000BEBB78                 MOV             X0, X19
LOAD:0000000000BEBB7C                 MOV             W2, WZR
LOAD:0000000000BEBB80                 BL              sub_62DD80
LOAD:0000000000BEBB84                 ADRP            X1, #qword_1533EE8@PAGE
LOAD:0000000000BEBB88                 LDR             X1, [X1,#qword_1533EE8@PAGEOFF]
LOAD:0000000000BEBB8C                 MOV             X0, X19
LOAD:0000000000BEBB90                 MOV             W2, WZR
LOAD:0000000000BEBB94                 BL              sub_626220
LOAD:0000000000BEBB98                 ADRL            X1, aJit ; "jit"
LOAD:0000000000BEBBA0                 MOV             X0, X19
LOAD:0000000000BEBBA4                 BL              sub_631C20
LOAD:0000000000BEBBA8                 MOV             W1, #1
LOAD:0000000000BEBBAC                 MOV             X0, X19
LOAD:0000000000BEBBB0                 MOV             W2, WZR
LOAD:0000000000BEBBB4                 BL              sub_62DD80
LOAD:0000000000BEBBB8                 ADRL            X2, aPreload ; "_PRELOAD"
LOAD:0000000000BEBBC0                 MOV             W1, #0xFFFFD8F0
LOAD:0000000000BEBBC4                 MOV             W3, #1
LOAD:0000000000BEBBC8                 MOV             X0, X19
LOAD:0000000000BEBBCC                 BL              sub_60E510
LOAD:0000000000BEBBD0                 ADRP            X1, #qword_1523EE0@PAGE
LOAD:0000000000BEBBD4                 LDR             X1, [X1,#qword_1523EE0@PAGEOFF]
LOAD:0000000000BEBBD8                 MOV             X0, X19
LOAD:0000000000BEBBDC                 MOV             W2, WZR
LOAD:0000000000BEBBE0                 BL              sub_626220
LOAD:0000000000BEBBE4                 ADRL            X2, word_1216A42
LOAD:0000000000BEBBEC                 MOV             W1, #0xFFFFFFFE
LOAD:0000000000BEBBF0                 MOV             X0, X19
LOAD:0000000000BEBBF4                 BL              sub_63AA50
LOAD:0000000000BEBBF8                 MOV             W1, #0xFFFFFFFE
LOAD:0000000000BEBBFC                 MOV             X0, X19
LOAD:0000000000BEBC00                 LDP             X19, X30, [SP+0x10+var_10],#0x10
LOAD:0000000000BEBC04                 B               sub_6348B0
LOAD:0000000000BEBC04 ; End of function luaL_openlibs
LOAD:0000000000BEBC04
LOAD:0000000000BEBC04 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBC08                 ALIGN 0x10
LOAD:0000000000BEBC10
LOAD:0000000000BEBC10 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEBC10
LOAD:0000000000BEBC10
LOAD:0000000000BEBC10 ; __int64 __fastcall BC_ISLT(long double)
LOAD:0000000000BEBC10 BC_ISLT                                 ; DATA XREF: sub_BCD3A8↑o
LOAD:0000000000BEBC10                                         ; sub_BCD3A8+C↑o ...
LOAD:0000000000BEBC10
LOAD:0000000000BEBC10 arg_8           =  8
LOAD:0000000000BEBC10
LOAD:0000000000BEBC10 ; FUNCTION CHUNK AT LOAD:0000000000BEE288 SIZE 00000054 BYTES
LOAD:0000000000BEBC10 ; FUNCTION CHUNK AT LOAD:0000000000BEE3B0 SIZE 00000018 BYTES
LOAD:0000000000BEBC10
LOAD:0000000000BEBC10                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBC14                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBC18                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEBC1C                 ADD             X21, X21, #4
LOAD:0000000000BEBC20                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBC24                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBC28                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBC2C                 B.NE            loc_BEBC58
LOAD:0000000000BEBC30                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBC34                 B.NE            loc_BEBC78
LOAD:0000000000BEBC38                 CMP             W0, W1
LOAD:0000000000BEBC3C                 CSEL            X21, X17, X21, LT
LOAD:0000000000BEBC40
LOAD:0000000000BEBC40 loc_BEBC40                              ; CODE XREF: BC_ISLT+7C↓j
LOAD:0000000000BEBC40                 LDR             W16, [X21],#4
LOAD:0000000000BEBC44                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBC48                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBC4C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBC50                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBC54                 BR              X8
LOAD:0000000000BEBC58 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBC58
LOAD:0000000000BEBC58 loc_BEBC58                              ; CODE XREF: BC_ISLT+1C↑j
LOAD:0000000000BEBC58                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEBC5C                 B.CC            loc_BEE288
LOAD:0000000000BEBC60                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBC64                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBC68                 B.HI            loc_BEBC84
LOAD:0000000000BEBC6C                 B.NE            loc_BEE288
LOAD:0000000000BEBC70                 SCVTF           D1, W1
LOAD:0000000000BEBC74                 B               loc_BEBC84
LOAD:0000000000BEBC78 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBC78
LOAD:0000000000BEBC78 loc_BEBC78                              ; CODE XREF: BC_ISLT+24↑j
LOAD:0000000000BEBC78                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBC7C                 B.CC            loc_BEE288
LOAD:0000000000BEBC80                 SCVTF           D0, W0
LOAD:0000000000BEBC84
LOAD:0000000000BEBC84 loc_BEBC84                              ; CODE XREF: BC_ISLT+58↑j
LOAD:0000000000BEBC84                                         ; BC_ISLT+64↑j
LOAD:0000000000BEBC84                 FCMP            D0, D1
LOAD:0000000000BEBC88                 CSEL            X21, X17, X21, CC
LOAD:0000000000BEBC8C                 B               loc_BEBC40
LOAD:0000000000BEBC8C ; End of function BC_ISLT
LOAD:0000000000BEBC8C
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90 ; __int64 __fastcall BC_ISGE(long double)
LOAD:0000000000BEBC90 BC_ISGE
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90 ; FUNCTION CHUNK AT LOAD:0000000000BEE288 SIZE 00000054 BYTES
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBC94                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBC98                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEBC9C                 ADD             X21, X21, #4
LOAD:0000000000BEBCA0                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBCA4                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBCA8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBCAC                 B.NE            loc_BEBCD8
LOAD:0000000000BEBCB0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBCB4                 B.NE            loc_BEBCF8
LOAD:0000000000BEBCB8                 CMP             W0, W1
LOAD:0000000000BEBCBC                 CSEL            X21, X17, X21, GE
LOAD:0000000000BEBCC0
LOAD:0000000000BEBCC0 loc_BEBCC0                              ; CODE XREF: BC_ISGE+7C↓j
LOAD:0000000000BEBCC0                 LDR             W16, [X21],#4
LOAD:0000000000BEBCC4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBCC8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBCCC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBCD0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBCD4                 BR              X8
LOAD:0000000000BEBCD8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBCD8
LOAD:0000000000BEBCD8 loc_BEBCD8                              ; CODE XREF: BC_ISGE+1C↑j
LOAD:0000000000BEBCD8                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEBCDC                 B.CC            loc_BEE288
LOAD:0000000000BEBCE0                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBCE4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBCE8                 B.HI            loc_BEBD04
LOAD:0000000000BEBCEC                 B.NE            loc_BEE288
LOAD:0000000000BEBCF0                 SCVTF           D1, W1
LOAD:0000000000BEBCF4                 B               loc_BEBD04
LOAD:0000000000BEBCF8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBCF8
LOAD:0000000000BEBCF8 loc_BEBCF8                              ; CODE XREF: BC_ISGE+24↑j
LOAD:0000000000BEBCF8                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBCFC                 B.CC            loc_BEE288
LOAD:0000000000BEBD00                 SCVTF           D0, W0
LOAD:0000000000BEBD04
LOAD:0000000000BEBD04 loc_BEBD04                              ; CODE XREF: BC_ISGE+58↑j
LOAD:0000000000BEBD04                                         ; BC_ISGE+64↑j
LOAD:0000000000BEBD04                 FCMP            D0, D1
LOAD:0000000000BEBD08                 CSEL            X21, X17, X21, CS
LOAD:0000000000BEBD0C                 B               loc_BEBCC0
LOAD:0000000000BEBD0C ; End of function BC_ISGE
LOAD:0000000000BEBD0C
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10 ; __int64 __fastcall BC_ISLE(long double)
LOAD:0000000000BEBD10 BC_ISLE
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10 ; FUNCTION CHUNK AT LOAD:0000000000BEE288 SIZE 00000054 BYTES
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBD14                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBD18                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEBD1C                 ADD             X21, X21, #4
LOAD:0000000000BEBD20                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBD24                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBD28                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBD2C                 B.NE            loc_BEBD58
LOAD:0000000000BEBD30                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBD34                 B.NE            loc_BEBD78
LOAD:0000000000BEBD38                 CMP             W0, W1
LOAD:0000000000BEBD3C                 CSEL            X21, X17, X21, LE
LOAD:0000000000BEBD40
LOAD:0000000000BEBD40 loc_BEBD40                              ; CODE XREF: BC_ISLE+7C↓j
LOAD:0000000000BEBD40                 LDR             W16, [X21],#4
LOAD:0000000000BEBD44                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBD48                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBD4C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBD50                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBD54                 BR              X8
LOAD:0000000000BEBD58 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBD58
LOAD:0000000000BEBD58 loc_BEBD58                              ; CODE XREF: BC_ISLE+1C↑j
LOAD:0000000000BEBD58                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEBD5C                 B.CC            loc_BEE288
LOAD:0000000000BEBD60                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBD64                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBD68                 B.HI            loc_BEBD84
LOAD:0000000000BEBD6C                 B.NE            loc_BEE288
LOAD:0000000000BEBD70                 SCVTF           D1, W1
LOAD:0000000000BEBD74                 B               loc_BEBD84
LOAD:0000000000BEBD78 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBD78
LOAD:0000000000BEBD78 loc_BEBD78                              ; CODE XREF: BC_ISLE+24↑j
LOAD:0000000000BEBD78                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBD7C                 B.CC            loc_BEE288
LOAD:0000000000BEBD80                 SCVTF           D0, W0
LOAD:0000000000BEBD84
LOAD:0000000000BEBD84 loc_BEBD84                              ; CODE XREF: BC_ISLE+58↑j
LOAD:0000000000BEBD84                                         ; BC_ISLE+64↑j
LOAD:0000000000BEBD84                 FCMP            D0, D1
LOAD:0000000000BEBD88                 CSEL            X21, X17, X21, LS
LOAD:0000000000BEBD8C                 B               loc_BEBD40
LOAD:0000000000BEBD8C ; End of function BC_ISLE
LOAD:0000000000BEBD8C
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90 ; __int64 __fastcall BC_ISGT(long double)
LOAD:0000000000BEBD90 BC_ISGT
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90 ; FUNCTION CHUNK AT LOAD:0000000000BEE288 SIZE 00000054 BYTES
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBD94                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBD98                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEBD9C                 ADD             X21, X21, #4
LOAD:0000000000BEBDA0                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBDA4                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBDA8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBDAC                 B.NE            loc_BEBDD8
LOAD:0000000000BEBDB0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBDB4                 B.NE            loc_BEBDF8
LOAD:0000000000BEBDB8                 CMP             W0, W1
LOAD:0000000000BEBDBC                 CSEL            X21, X17, X21, GT
LOAD:0000000000BEBDC0
LOAD:0000000000BEBDC0 loc_BEBDC0                              ; CODE XREF: BC_ISGT+7C↓j
LOAD:0000000000BEBDC0                 LDR             W16, [X21],#4
LOAD:0000000000BEBDC4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBDC8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBDCC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBDD0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBDD4                 BR              X8
LOAD:0000000000BEBDD8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBDD8
LOAD:0000000000BEBDD8 loc_BEBDD8                              ; CODE XREF: BC_ISGT+1C↑j
LOAD:0000000000BEBDD8                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEBDDC                 B.CC            loc_BEE288
LOAD:0000000000BEBDE0                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBDE4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBDE8                 B.HI            loc_BEBE04
LOAD:0000000000BEBDEC                 B.NE            loc_BEE288
LOAD:0000000000BEBDF0                 SCVTF           D1, W1
LOAD:0000000000BEBDF4                 B               loc_BEBE04
LOAD:0000000000BEBDF8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBDF8
LOAD:0000000000BEBDF8 loc_BEBDF8                              ; CODE XREF: BC_ISGT+24↑j
LOAD:0000000000BEBDF8                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBDFC                 B.CC            loc_BEE288
LOAD:0000000000BEBE00                 SCVTF           D0, W0
LOAD:0000000000BEBE04
LOAD:0000000000BEBE04 loc_BEBE04                              ; CODE XREF: BC_ISGT+58↑j
LOAD:0000000000BEBE04                                         ; BC_ISGT+64↑j
LOAD:0000000000BEBE04                 FCMP            D0, D1
LOAD:0000000000BEBE08                 CSEL            X21, X17, X21, HI
LOAD:0000000000BEBE0C                 B               loc_BEBDC0
LOAD:0000000000BEBE0C ; End of function BC_ISGT
LOAD:0000000000BEBE0C
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10 ; __int64 BC_ISEQV()
LOAD:0000000000BEBE10 BC_ISEQV
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10 arg_8           =  8
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10 ; FUNCTION CHUNK AT LOAD:0000000000BEC05C SIZE 00000070 BYTES
LOAD:0000000000BEBE10 ; FUNCTION CHUNK AT LOAD:0000000000BEE310 SIZE 00000038 BYTES
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBE14                 ADD             X28, X19, X28,LSL#3
LOAD:0000000000BEBE18                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBE1C                 LDR             X2, [X28]
LOAD:0000000000BEBE20                 ADD             X21, X21, #4
LOAD:0000000000BEBE24                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBE28                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBE2C                 ASR             X15, X2, #0x2F ; '/'
LOAD:0000000000BEBE30                 CMN             X15, #0xE
LOAD:0000000000BEBE34                 B.LS            loc_BEBFD0
LOAD:0000000000BEBE38                 ASR             X8, X0, #0x2F ; '/'
LOAD:0000000000BEBE3C                 CMN             X15, #0xB
LOAD:0000000000BEBE40                 CCMN            X8, #0xB, #4, NE
LOAD:0000000000BEBE44                 B.EQ            loc_BEE32C
LOAD:0000000000BEBE48                 CMP             X0, X2
LOAD:0000000000BEBE4C                 B.NE            loc_BEBE6C
LOAD:0000000000BEBE50
LOAD:0000000000BEBE50 loc_BEBE50                              ; CODE XREF: BC_ISEQV+E4↓j
LOAD:0000000000BEBE50                                         ; BC_ISEQV+F0↓j ...
LOAD:0000000000BEBE50                 MOV             X21, X17
LOAD:0000000000BEBE54
LOAD:0000000000BEBE54 loc_BEBE54                              ; CODE XREF: BC_ISEQV+64↓j
LOAD:0000000000BEBE54                                         ; BC_ISEQV+70↓j ...
LOAD:0000000000BEBE54                 LDR             W16, [X21],#4
LOAD:0000000000BEBE58                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBE5C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBE60                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBE64                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBE68                 BR              X8
LOAD:0000000000BEBE6C ; ---------------------------------------------------------------------------
LOAD:0000000000BEBE6C
LOAD:0000000000BEBE6C loc_BEBE6C                              ; CODE XREF: BC_ISEQV+3C↑j
LOAD:0000000000BEBE6C                 CMP             X15, X8
LOAD:0000000000BEBE70                 CCMN            X15, #0xC, #2, EQ
LOAD:0000000000BEBE74                 B.HI            loc_BEBE54
LOAD:0000000000BEBE78                 AND             X1, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEBE7C                 LDR             X10, [X1,#0x20]
LOAD:0000000000BEBE80                 CBZ             X10, loc_BEBE54
LOAD:0000000000BEBE84                 LDRB            W9, [X10,#0xA]
LOAD:0000000000BEBE88                 MOV             W3, #0
LOAD:0000000000BEBE8C                 TBNZ            W9, #4, loc_BEBE54
LOAD:0000000000BEBE90                 B               loc_BEE310
LOAD:0000000000BEBE94 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBE94
LOAD:0000000000BEBE94 BC_ISNEV
LOAD:0000000000BEBE94                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBE98                 ADD             X28, X19, X28,LSL#3
LOAD:0000000000BEBE9C                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBEA0                 LDR             X2, [X28]
LOAD:0000000000BEBEA4                 ADD             X21, X21, #4
LOAD:0000000000BEBEA8                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBEAC                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBEB0                 ASR             X15, X2, #0x2F ; '/'
LOAD:0000000000BEBEB4                 CMN             X15, #0xE
LOAD:0000000000BEBEB8                 B.LS            loc_BEC05C
LOAD:0000000000BEBEBC                 ASR             X8, X0, #0x2F ; '/'
LOAD:0000000000BEBEC0                 CMN             X15, #0xB
LOAD:0000000000BEBEC4                 CCMN            X8, #0xB, #4, NE
LOAD:0000000000BEBEC8                 B.EQ            loc_BEE32C
LOAD:0000000000BEBECC                 CMP             X0, X2
LOAD:0000000000BEBED0                 B.NE            loc_BEBEEC
LOAD:0000000000BEBED4                 LDR             W16, [X21],#4
LOAD:0000000000BEBED8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBEDC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBEE0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBEE4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBEE8                 BR              X8
LOAD:0000000000BEBEEC ; ---------------------------------------------------------------------------
LOAD:0000000000BEBEEC
LOAD:0000000000BEBEEC loc_BEBEEC                              ; CODE XREF: BC_ISEQV+C0↑j
LOAD:0000000000BEBEEC                 CMP             X15, X8
LOAD:0000000000BEBEF0                 CCMN            X15, #0xC, #2, EQ
LOAD:0000000000BEBEF4                 B.HI            loc_BEBE50
LOAD:0000000000BEBEF8                 AND             X1, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEBEFC                 LDR             X10, [X1,#0x20]
LOAD:0000000000BEBF00                 CBZ             X10, loc_BEBE50
LOAD:0000000000BEBF04                 LDRB            W9, [X10,#0xA]
LOAD:0000000000BEBF08                 MOV             W3, #1
LOAD:0000000000BEBF0C                 TBNZ            W9, #4, loc_BEBE50
LOAD:0000000000BEBF10                 B               loc_BEE310
LOAD:0000000000BEBF14 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBF14
LOAD:0000000000BEBF14 BC_ISEQS
LOAD:0000000000BEBF14                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBF18                 MVN             X28, X28
LOAD:0000000000BEBF1C                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBF20                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEBF24                 ADD             X21, X21, #4
LOAD:0000000000BEBF28                 MOV             X8, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEBF2C                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEBF30                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBF34                 ADD             X1, X1, X8,LSL#47
LOAD:0000000000BEBF38                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBF3C                 CMN             X15, #0xB
LOAD:0000000000BEBF40                 B.EQ            loc_BEE32C
LOAD:0000000000BEBF44                 CMP             X0, X1
LOAD:0000000000BEBF48                 CSEL            X21, X17, X21, EQ
LOAD:0000000000BEBF4C                 LDR             W16, [X21],#4
LOAD:0000000000BEBF50                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBF54                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBF58                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBF5C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBF60                 BR              X8
LOAD:0000000000BEBF64 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBF64
LOAD:0000000000BEBF64 BC_ISNES
LOAD:0000000000BEBF64                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBF68                 MVN             X28, X28
LOAD:0000000000BEBF6C                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBF70                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEBF74                 ADD             X21, X21, #4
LOAD:0000000000BEBF78                 MOV             X8, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEBF7C                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEBF80                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBF84                 ADD             X1, X1, X8,LSL#47
LOAD:0000000000BEBF88                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBF8C                 CMN             X15, #0xB
LOAD:0000000000BEBF90                 B.EQ            loc_BEE32C
LOAD:0000000000BEBF94                 CMP             X0, X1
LOAD:0000000000BEBF98                 CSEL            X21, X17, X21, NE
LOAD:0000000000BEBF9C                 LDR             W16, [X21],#4
LOAD:0000000000BEBFA0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBFA4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBFA8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBFAC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBFB0                 BR              X8
LOAD:0000000000BEBFB4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBFB4
LOAD:0000000000BEBFB4 BC_ISEQN
LOAD:0000000000BEBFB4                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBFB8                 ADD             X28, X20, X28,LSL#3
LOAD:0000000000BEBFBC                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBFC0                 LDR             X2, [X28]
LOAD:0000000000BEBFC4                 ADD             X21, X21, #4
LOAD:0000000000BEBFC8                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBFCC                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBFD0
LOAD:0000000000BEBFD0 loc_BEBFD0                              ; CODE XREF: BC_ISEQV+24↑j
LOAD:0000000000BEBFD0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBFD4                 B.NE            loc_BEC000
LOAD:0000000000BEBFD8                 CMP             X25, X2,LSR#32
LOAD:0000000000BEBFDC                 B.NE            loc_BEC020
LOAD:0000000000BEBFE0                 CMP             W0, W2
LOAD:0000000000BEBFE4
LOAD:0000000000BEBFE4 loc_BEBFE4                              ; CODE XREF: BC_ISEQV+20C↓j
LOAD:0000000000BEBFE4                                         ; BC_ISEQV+21C↓j
LOAD:0000000000BEBFE4                 CSEL            X21, X17, X21, EQ
LOAD:0000000000BEBFE8
LOAD:0000000000BEBFE8 loc_BEBFE8                              ; CODE XREF: BC_ISEQV+228↓j
LOAD:0000000000BEBFE8                 LDR             W16, [X21],#4
LOAD:0000000000BEBFEC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBFF0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBFF4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBFF8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBFFC                 BR              X8
LOAD:0000000000BEC000 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC000
LOAD:0000000000BEC000 loc_BEC000                              ; CODE XREF: BC_ISEQV+1C4↑j
LOAD:0000000000BEC000                 B.CC            loc_BEC030
LOAD:0000000000BEC004                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC008                 LDR             D1, [X28]
LOAD:0000000000BEC00C                 CMP             X25, X2,LSR#32
LOAD:0000000000BEC010                 B.NE            loc_BEC018
LOAD:0000000000BEC014                 SCVTF           D1, W2
LOAD:0000000000BEC018
LOAD:0000000000BEC018 loc_BEC018                              ; CODE XREF: BC_ISEQV+200↑j
LOAD:0000000000BEC018                 FCMP            D0, D1
LOAD:0000000000BEC01C                 B               loc_BEBFE4
LOAD:0000000000BEC020 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC020
LOAD:0000000000BEC020 loc_BEC020                              ; CODE XREF: BC_ISEQV+1CC↑j
LOAD:0000000000BEC020                 LDR             D1, [X28]
LOAD:0000000000BEC024                 SCVTF           D0, W0
LOAD:0000000000BEC028                 FCMP            D0, D1
LOAD:0000000000BEC02C                 B               loc_BEBFE4
LOAD:0000000000BEC030 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC030
LOAD:0000000000BEC030 loc_BEC030                              ; CODE XREF: BC_ISEQV:loc_BEC000↑j
LOAD:0000000000BEC030                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEC034                 CMN             X15, #0xB
LOAD:0000000000BEC038                 B.NE            loc_BEBFE8
LOAD:0000000000BEC03C                 B               loc_BEE32C
LOAD:0000000000BEC03C ; End of function BC_ISEQV
LOAD:0000000000BEC03C
LOAD:0000000000BEC040
LOAD:0000000000BEC040 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC040
LOAD:0000000000BEC040
LOAD:0000000000BEC040 ; void BC_ISNEN()
LOAD:0000000000BEC040 BC_ISNEN
LOAD:0000000000BEC040                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC044                 ADD             X28, X20, X28,LSL#3
LOAD:0000000000BEC048                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC04C                 LDR             X2, [X28]
LOAD:0000000000BEC050                 ADD             X21, X21, #4
LOAD:0000000000BEC054                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC058                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC058 ; End of function BC_ISNEN
LOAD:0000000000BEC058
LOAD:0000000000BEC05C ; START OF FUNCTION CHUNK FOR BC_ISEQV
LOAD:0000000000BEC05C
LOAD:0000000000BEC05C loc_BEC05C                              ; CODE XREF: BC_ISEQV+A8↑j
LOAD:0000000000BEC05C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC060                 B.NE            loc_BEC08C
LOAD:0000000000BEC064                 CMP             X25, X2,LSR#32
LOAD:0000000000BEC068                 B.NE            loc_BEC0AC
LOAD:0000000000BEC06C                 CMP             W0, W2
LOAD:0000000000BEC070
LOAD:0000000000BEC070 loc_BEC070                              ; CODE XREF: BC_ISEQV+298↓j
LOAD:0000000000BEC070                                         ; BC_ISEQV+2A8↓j ...
LOAD:0000000000BEC070                 CSEL            X21, X17, X21, NE
LOAD:0000000000BEC074                 LDR             W16, [X21],#4
LOAD:0000000000BEC078                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC07C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC080                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC084                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC088                 BR              X8
LOAD:0000000000BEC08C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC08C
LOAD:0000000000BEC08C loc_BEC08C                              ; CODE XREF: BC_ISEQV+250↑j
LOAD:0000000000BEC08C                 B.CC            loc_BEC0BC
LOAD:0000000000BEC090                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC094                 LDR             D1, [X28]
LOAD:0000000000BEC098                 CMP             X25, X2,LSR#32
LOAD:0000000000BEC09C                 B.NE            loc_BEC0A4
LOAD:0000000000BEC0A0                 SCVTF           D1, W2
LOAD:0000000000BEC0A4
LOAD:0000000000BEC0A4 loc_BEC0A4                              ; CODE XREF: BC_ISEQV+28C↑j
LOAD:0000000000BEC0A4                 FCMP            D0, D1
LOAD:0000000000BEC0A8                 B               loc_BEC070
LOAD:0000000000BEC0AC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC0AC
LOAD:0000000000BEC0AC loc_BEC0AC                              ; CODE XREF: BC_ISEQV+258↑j
LOAD:0000000000BEC0AC                 LDR             D1, [X28]
LOAD:0000000000BEC0B0                 SCVTF           D0, W0
LOAD:0000000000BEC0B4                 FCMP            D0, D1
LOAD:0000000000BEC0B8                 B               loc_BEC070
LOAD:0000000000BEC0BC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC0BC
LOAD:0000000000BEC0BC loc_BEC0BC                              ; CODE XREF: BC_ISEQV:loc_BEC08C↑j
LOAD:0000000000BEC0BC                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEC0C0                 CMN             X15, #0xB
LOAD:0000000000BEC0C4                 B.NE            loc_BEC070
LOAD:0000000000BEC0C8                 B               loc_BEE32C
LOAD:0000000000BEC0C8 ; END OF FUNCTION CHUNK FOR BC_ISEQV
LOAD:0000000000BEC0CC
LOAD:0000000000BEC0CC ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC0CC
LOAD:0000000000BEC0CC
LOAD:0000000000BEC0CC ; __int64 BC_ISEQP()
LOAD:0000000000BEC0CC BC_ISEQP
LOAD:0000000000BEC0CC                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC0D0                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC0D4                 ADD             X21, X21, #4
LOAD:0000000000BEC0D8                 ADD             X28, X28, #1
LOAD:0000000000BEC0DC                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC0E0                 ASR             X15, X8, #0x2F ; '/'
LOAD:0000000000BEC0E4                 CMN             X15, #0xB
LOAD:0000000000BEC0E8                 B.EQ            loc_BEE32C
LOAD:0000000000BEC0EC                 CMN             X28, X15
LOAD:0000000000BEC0F0                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC0F4                 CSEL            X21, X17, X21, EQ
LOAD:0000000000BEC0F8                 LDR             W16, [X21],#4
LOAD:0000000000BEC0FC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC100                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC104                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC108                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC10C                 BR              X8
LOAD:0000000000BEC10C ; End of function BC_ISEQP
LOAD:0000000000BEC10C
LOAD:0000000000BEC110
LOAD:0000000000BEC110 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC110
LOAD:0000000000BEC110
LOAD:0000000000BEC110 ; __int64 BC_ISNEP()
LOAD:0000000000BEC110 BC_ISNEP
LOAD:0000000000BEC110                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC114                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC118                 ADD             X21, X21, #4
LOAD:0000000000BEC11C                 ADD             X28, X28, #1
LOAD:0000000000BEC120                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC124                 ASR             X15, X8, #0x2F ; '/'
LOAD:0000000000BEC128                 CMN             X15, #0xB
LOAD:0000000000BEC12C                 B.EQ            loc_BEE32C
LOAD:0000000000BEC130                 CMN             X28, X15
LOAD:0000000000BEC134                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC138                 CSEL            X21, X17, X21, NE
LOAD:0000000000BEC13C                 LDR             W16, [X21],#4
LOAD:0000000000BEC140                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC144                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC148                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC14C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC150                 BR              X8
LOAD:0000000000BEC150 ; End of function BC_ISNEP
LOAD:0000000000BEC150
LOAD:0000000000BEC154
LOAD:0000000000BEC154 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC154
LOAD:0000000000BEC154
LOAD:0000000000BEC154 ; __int64 BC_ISFC()
LOAD:0000000000BEC154 BC_ISFC
LOAD:0000000000BEC154                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC158                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC15C                 ADD             X21, X21, #4
LOAD:0000000000BEC160                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC164                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC168                 CMP             X8, X9
LOAD:0000000000BEC16C                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC170                 CSEL            X27, X27, X28, CS
LOAD:0000000000BEC174                 CSEL            X21, X17, X21, CS
LOAD:0000000000BEC178                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC17C                 LDR             W16, [X21],#4
LOAD:0000000000BEC180                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC184                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC188                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC18C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC190                 BR              X8
LOAD:0000000000BEC190 ; End of function BC_ISFC
LOAD:0000000000BEC190
LOAD:0000000000BEC194
LOAD:0000000000BEC194 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC194
LOAD:0000000000BEC194
LOAD:0000000000BEC194 ; __int64 BC_ISTC()
LOAD:0000000000BEC194 BC_ISTC
LOAD:0000000000BEC194                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC198                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC19C                 ADD             X21, X21, #4
LOAD:0000000000BEC1A0                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC1A4                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC1A8                 CMP             X8, X9
LOAD:0000000000BEC1AC                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC1B0                 CSEL            X27, X27, X28, CC
LOAD:0000000000BEC1B4                 CSEL            X21, X17, X21, CC
LOAD:0000000000BEC1B8                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC1BC                 LDR             W16, [X21],#4
LOAD:0000000000BEC1C0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC1C4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC1C8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC1CC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC1D0                 BR              X8
LOAD:0000000000BEC1D0 ; End of function BC_ISTC
LOAD:0000000000BEC1D0
LOAD:0000000000BEC1D4
LOAD:0000000000BEC1D4 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC1D4
LOAD:0000000000BEC1D4
LOAD:0000000000BEC1D4 ; __int64 BC_ISF()
LOAD:0000000000BEC1D4 BC_ISF
LOAD:0000000000BEC1D4                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC1D8                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC1DC                 ADD             X21, X21, #4
LOAD:0000000000BEC1E0                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC1E4                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC1E8                 CMP             X8, X9
LOAD:0000000000BEC1EC                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC1F0                 CSEL            X21, X17, X21, CS
LOAD:0000000000BEC1F4                 LDR             W16, [X21],#4
LOAD:0000000000BEC1F8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC1FC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC200                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC204                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC208                 BR              X8
LOAD:0000000000BEC208 ; End of function BC_ISF
LOAD:0000000000BEC208
LOAD:0000000000BEC20C
LOAD:0000000000BEC20C ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC20C
LOAD:0000000000BEC20C
LOAD:0000000000BEC20C ; __int64 BC_IST()
LOAD:0000000000BEC20C BC_IST
LOAD:0000000000BEC20C                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC210                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC214                 ADD             X21, X21, #4
LOAD:0000000000BEC218                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC21C                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC220                 CMP             X8, X9
LOAD:0000000000BEC224                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC228                 CSEL            X21, X17, X21, CC
LOAD:0000000000BEC22C                 LDR             W16, [X21],#4
LOAD:0000000000BEC230                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC234                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC238                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC23C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC240                 BR              X8
LOAD:0000000000BEC240 ; End of function BC_IST
LOAD:0000000000BEC240
LOAD:0000000000BEC244 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC244
LOAD:0000000000BEC244 BC_ISTYPE
LOAD:0000000000BEC244                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC248                 CMN             X28, X8,ASR#47
LOAD:0000000000BEC24C                 B.NE            loc_BEE348
LOAD:0000000000BEC250                 LDR             W16, [X21],#4
LOAD:0000000000BEC254                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC258                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC25C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC260                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC264                 BR              X8
LOAD:0000000000BEC268 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC268
LOAD:0000000000BEC268 BC_ISNUM
LOAD:0000000000BEC268                 LDR             X8, [X19,X27]
LOAD:0000000000BEC26C                 CMP             X25, X8,LSR#32
LOAD:0000000000BEC270                 B.LS            loc_BEE348
LOAD:0000000000BEC274                 LDR             W16, [X21],#4
LOAD:0000000000BEC278                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC27C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC280                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC284                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC288                 BR              X8
LOAD:0000000000BEC28C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC28C
LOAD:0000000000BEC28C BC_NOT
LOAD:0000000000BEC28C                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC290                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC294                 MOV             X10, #0xFFFEFFFFFFFFFFFF
LOAD:0000000000BEC298                 CMP             X8, X9
LOAD:0000000000BEC29C                 CSEL            X8, X9, X10, CC
LOAD:0000000000BEC2A0                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC2A4                 LDR             W16, [X21],#4
LOAD:0000000000BEC2A8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC2AC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC2B0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC2B4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC2B8                 BR              X8
LOAD:0000000000BEC2BC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC2BC
LOAD:0000000000BEC2BC BC_MOV
LOAD:0000000000BEC2BC                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC2C0                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC2C4                 LDR             W16, [X21],#4
LOAD:0000000000BEC2C8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC2CC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC2D0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC2D4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC2D8                 BR              X8
LOAD:0000000000BEC2DC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC2DC
LOAD:0000000000BEC2DC BC_LEN
LOAD:0000000000BEC2DC                 LDR             X0, [X19,X28,LSL#3]
LOAD:0000000000BEC2E0                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEC2E4                 CMN             X15, #5
LOAD:0000000000BEC2E8                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEC2EC                 B.NE            loc_BEC314
LOAD:0000000000BEC2F0                 LDR             W0, [X0,#0x14]
LOAD:0000000000BEC2F4
LOAD:0000000000BEC2F4 loc_BEC2F4                              ; CODE XREF: LOAD:0000000000BEC320↓j
LOAD:0000000000BEC2F4                 ADD             X0, X0, X24
LOAD:0000000000BEC2F8                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC2FC                 LDR             W16, [X21],#4
LOAD:0000000000BEC300                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC304                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC308                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC30C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC310                 BR              X8
LOAD:0000000000BEC314 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC314
LOAD:0000000000BEC314 loc_BEC314                              ; CODE XREF: LOAD:0000000000BEC2EC↑j
LOAD:0000000000BEC314                 CMN             X15, #0xC
LOAD:0000000000BEC318                 B.NE            loc_BEE3C8
LOAD:0000000000BEC31C                 BL              sub_BF35BC
LOAD:0000000000BEC320                 B               loc_BEC2F4
LOAD:0000000000BEC324 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC324
LOAD:0000000000BEC324 BC_UNM
LOAD:0000000000BEC324                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC328                 ASR             X15, X8, #0x2F ; '/'
LOAD:0000000000BEC32C                 CMN             X15, #0xE
LOAD:0000000000BEC330                 B.HI            loc_BEE380
LOAD:0000000000BEC334                 EOR             X8, X8, #0x8000000000000000
LOAD:0000000000BEC338                 B.NE            asdwas
LOAD:0000000000BEC33C                 NEGS            W8, W8
LOAD:0000000000BEC340                 MOV             X2, #0x41E0000000000000
LOAD:0000000000BEC344                 ADD             X8, X8, X24
LOAD:0000000000BEC348                 CSEL            X8, X8, X2, VC
LOAD:0000000000BEC34C
LOAD:0000000000BEC34C asdwas                                  ; CODE XREF: LOAD:0000000000BEC338↑j
LOAD:0000000000BEC34C                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC350                 LDR             W16, [X21],#4
LOAD:0000000000BEC354                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC358                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC35C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC360                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC364                 BR              X8
LOAD:0000000000BEC368 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC368
LOAD:0000000000BEC368 BC_ADDVN
LOAD:0000000000BEC368                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC36C                 AND             X28, X28, #0xFF
LOAD:0000000000BEC370                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC374                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC378                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC37C                 B.NE            loc_BEC3B0
LOAD:0000000000BEC380                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC384                 B.NE            loc_BEC3B0
LOAD:0000000000BEC388                 ADDS            W0, W0, W1
LOAD:0000000000BEC38C                 B.VS            loc_BEE368
LOAD:0000000000BEC390                 ADD             X0, X0, X24
LOAD:0000000000BEC394                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC398
LOAD:0000000000BEC398 loc_BEC398                              ; CODE XREF: LOAD:0000000000BEC3D0↓j
LOAD:0000000000BEC398                 LDR             W16, [X21],#4
LOAD:0000000000BEC39C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC3A0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC3A4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC3A8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC3AC                 BR              X8
LOAD:0000000000BEC3B0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC3B0
LOAD:0000000000BEC3B0 loc_BEC3B0                              ; CODE XREF: LOAD:0000000000BEC37C↑j
LOAD:0000000000BEC3B0                                         ; LOAD:0000000000BEC384↑j
LOAD:0000000000BEC3B0                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC3B4                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC3B8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC3BC                 B.LS            loc_BEE368
LOAD:0000000000BEC3C0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC3C4                 B.LS            loc_BEE368
LOAD:0000000000BEC3C8                 FADD            D0, D0, D1
LOAD:0000000000BEC3CC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC3D0                 B               loc_BEC398
LOAD:0000000000BEC3D4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC3D4
LOAD:0000000000BEC3D4 BC_SUBVN
LOAD:0000000000BEC3D4                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC3D8                 AND             X28, X28, #0xFF
LOAD:0000000000BEC3DC                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC3E0                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC3E4                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC3E8                 B.NE            loc_BEC41C
LOAD:0000000000BEC3EC                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC3F0                 B.NE            loc_BEC41C
LOAD:0000000000BEC3F4                 SUBS            W0, W0, W1
LOAD:0000000000BEC3F8                 B.VS            loc_BEE368
LOAD:0000000000BEC3FC                 ADD             X0, X0, X24
LOAD:0000000000BEC400                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC404
LOAD:0000000000BEC404 loc_BEC404                              ; CODE XREF: LOAD:0000000000BEC43C↓j
LOAD:0000000000BEC404                 LDR             W16, [X21],#4
LOAD:0000000000BEC408                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC40C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC410                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC414                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC418                 BR              X8
LOAD:0000000000BEC41C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC41C
LOAD:0000000000BEC41C loc_BEC41C                              ; CODE XREF: LOAD:0000000000BEC3E8↑j
LOAD:0000000000BEC41C                                         ; LOAD:0000000000BEC3F0↑j
LOAD:0000000000BEC41C                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC420                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC424                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC428                 B.LS            loc_BEE368
LOAD:0000000000BEC42C                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC430                 B.LS            loc_BEE368
LOAD:0000000000BEC434                 FSUB            D0, D0, D1
LOAD:0000000000BEC438                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC43C                 B               loc_BEC404
LOAD:0000000000BEC440 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC440
LOAD:0000000000BEC440 BC_MULVN
LOAD:0000000000BEC440                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC444                 AND             X28, X28, #0xFF
LOAD:0000000000BEC448                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC44C                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC450                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC454                 B.NE            loc_BEC490
LOAD:0000000000BEC458                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC45C                 B.NE            loc_BEC490
LOAD:0000000000BEC460                 SMULL           X0, W0, W1
LOAD:0000000000BEC464                 CMP             X0, W0,SXTW
LOAD:0000000000BEC468                 MOV             W0, W0
LOAD:0000000000BEC46C                 B.NE            loc_BEE368
LOAD:0000000000BEC470                 ADD             X0, X0, X24
LOAD:0000000000BEC474                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC478
LOAD:0000000000BEC478 loc_BEC478                              ; CODE XREF: LOAD:0000000000BEC4B0↓j
LOAD:0000000000BEC478                 LDR             W16, [X21],#4
LOAD:0000000000BEC47C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC480                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC484                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC488                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC48C                 BR              X8
LOAD:0000000000BEC490 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC490
LOAD:0000000000BEC490 loc_BEC490                              ; CODE XREF: LOAD:0000000000BEC454↑j
LOAD:0000000000BEC490                                         ; LOAD:0000000000BEC45C↑j
LOAD:0000000000BEC490                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC494                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC498                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC49C                 B.LS            loc_BEE368
LOAD:0000000000BEC4A0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC4A4                 B.LS            loc_BEE368
LOAD:0000000000BEC4A8                 FMUL            D0, D0, D1
LOAD:0000000000BEC4AC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC4B0                 B               loc_BEC478
LOAD:0000000000BEC4B4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC4B4
LOAD:0000000000BEC4B4 BC_DIVVN
LOAD:0000000000BEC4B4                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC4B8                 AND             X28, X28, #0xFF
LOAD:0000000000BEC4BC                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC4C0                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC4C4                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC4C8                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC4CC                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC4D0                 B.LS            loc_BEE368
LOAD:0000000000BEC4D4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC4D8                 B.LS            loc_BEE368
LOAD:0000000000BEC4DC                 FDIV            D0, D0, D1
LOAD:0000000000BEC4E0                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC4E4                 LDR             W16, [X21],#4
LOAD:0000000000BEC4E8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC4EC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC4F0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC4F4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC4F8                 BR              X8
LOAD:0000000000BEC4FC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC4FC
LOAD:0000000000BEC4FC BC_MODVN
LOAD:0000000000BEC4FC                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC500                 AND             X28, X28, #0xFF
LOAD:0000000000BEC504                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC508                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC50C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC510                 B.NE            loc_BEC544
LOAD:0000000000BEC514                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC518                 B.NE            loc_BEC544
LOAD:0000000000BEC51C                 CBZ             W1, loc_BEE368
LOAD:0000000000BEC520                 BL              sub_BEF7FC
LOAD:0000000000BEC524                 ADD             X0, X0, X24
LOAD:0000000000BEC528                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC52C
LOAD:0000000000BEC52C loc_BEC52C                              ; CODE XREF: LOAD:0000000000BEC56C↓j
LOAD:0000000000BEC52C                 LDR             W16, [X21],#4
LOAD:0000000000BEC530                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC534                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC538                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC53C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC540                 BR              X8
LOAD:0000000000BEC544 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC544
LOAD:0000000000BEC544 loc_BEC544                              ; CODE XREF: LOAD:0000000000BEC510↑j
LOAD:0000000000BEC544                                         ; LOAD:0000000000BEC518↑j
LOAD:0000000000BEC544                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC548                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC54C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC550                 B.LS            loc_BEE368
LOAD:0000000000BEC554                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC558                 B.LS            loc_BEE368
LOAD:0000000000BEC55C                 FDIV            D2, D0, D1
LOAD:0000000000BEC560                 FRINTM          D2, D2
LOAD:0000000000BEC564                 FMSUB           D0, D2, D1, D0
LOAD:0000000000BEC568                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC56C                 B               loc_BEC52C
LOAD:0000000000BEC570 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC570
LOAD:0000000000BEC570 BC_ADDNV
LOAD:0000000000BEC570                 AND             X28, X28, #0xFF
LOAD:0000000000BEC574                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC578                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC57C                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC580                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC584                 B.NE            loc_BEC5B8
LOAD:0000000000BEC588                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC58C                 B.NE            loc_BEC5B8
LOAD:0000000000BEC590                 ADDS            W0, W0, W1
LOAD:0000000000BEC594                 B.VS            loc_BEE374
LOAD:0000000000BEC598                 ADD             X0, X0, X24
LOAD:0000000000BEC59C                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC5A0
LOAD:0000000000BEC5A0 loc_BEC5A0                              ; CODE XREF: LOAD:0000000000BEC5D8↓j
LOAD:0000000000BEC5A0                 LDR             W16, [X21],#4
LOAD:0000000000BEC5A4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC5A8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC5AC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC5B0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC5B4                 BR              X8
LOAD:0000000000BEC5B8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC5B8
LOAD:0000000000BEC5B8 loc_BEC5B8                              ; CODE XREF: LOAD:0000000000BEC584↑j
LOAD:0000000000BEC5B8                                         ; LOAD:0000000000BEC58C↑j
LOAD:0000000000BEC5B8                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC5BC                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC5C0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC5C4                 B.LS            loc_BEE374
LOAD:0000000000BEC5C8                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC5CC                 B.LS            loc_BEE374
LOAD:0000000000BEC5D0                 FADD            D0, D0, D1
LOAD:0000000000BEC5D4                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC5D8                 B               loc_BEC5A0
LOAD:0000000000BEC5DC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC5DC
LOAD:0000000000BEC5DC BC_SUBNV
LOAD:0000000000BEC5DC                 AND             X28, X28, #0xFF
LOAD:0000000000BEC5E0                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC5E4                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC5E8                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC5EC                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC5F0                 B.NE            loc_BEC624
LOAD:0000000000BEC5F4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC5F8                 B.NE            loc_BEC624
LOAD:0000000000BEC5FC                 SUBS            W0, W0, W1
LOAD:0000000000BEC600                 B.VS            loc_BEE374
LOAD:0000000000BEC604                 ADD             X0, X0, X24
LOAD:0000000000BEC608                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC60C
LOAD:0000000000BEC60C loc_BEC60C                              ; CODE XREF: LOAD:0000000000BEC644↓j
LOAD:0000000000BEC60C                 LDR             W16, [X21],#4
LOAD:0000000000BEC610                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC614                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC618                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC61C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC620                 BR              X8
LOAD:0000000000BEC624 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC624
LOAD:0000000000BEC624 loc_BEC624                              ; CODE XREF: LOAD:0000000000BEC5F0↑j
LOAD:0000000000BEC624                                         ; LOAD:0000000000BEC5F8↑j
LOAD:0000000000BEC624                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC628                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC62C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC630                 B.LS            loc_BEE374
LOAD:0000000000BEC634                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC638                 B.LS            loc_BEE374
LOAD:0000000000BEC63C                 FSUB            D0, D0, D1
LOAD:0000000000BEC640                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC644                 B               loc_BEC60C
LOAD:0000000000BEC648 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC648
LOAD:0000000000BEC648 BC_MULNV
LOAD:0000000000BEC648                 AND             X28, X28, #0xFF
LOAD:0000000000BEC64C                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC650                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC654                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC658                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC65C                 B.NE            loc_BEC698
LOAD:0000000000BEC660                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC664                 B.NE            loc_BEC698
LOAD:0000000000BEC668                 SMULL           X0, W0, W1
LOAD:0000000000BEC66C                 CMP             X0, W0,SXTW
LOAD:0000000000BEC670                 MOV             W0, W0
LOAD:0000000000BEC674                 B.NE            loc_BEE374
LOAD:0000000000BEC678                 ADD             X0, X0, X24
LOAD:0000000000BEC67C                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC680
LOAD:0000000000BEC680 loc_BEC680                              ; CODE XREF: LOAD:0000000000BEC6B8↓j
LOAD:0000000000BEC680                 LDR             W16, [X21],#4
LOAD:0000000000BEC684                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC688                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC68C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC690                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC694                 BR              X8
LOAD:0000000000BEC698 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC698
LOAD:0000000000BEC698 loc_BEC698                              ; CODE XREF: LOAD:0000000000BEC65C↑j
LOAD:0000000000BEC698                                         ; LOAD:0000000000BEC664↑j
LOAD:0000000000BEC698                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC69C                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC6A0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC6A4                 B.LS            loc_BEE374
LOAD:0000000000BEC6A8                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC6AC                 B.LS            loc_BEE374
LOAD:0000000000BEC6B0                 FMUL            D0, D0, D1
LOAD:0000000000BEC6B4                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC6B8                 B               loc_BEC680
LOAD:0000000000BEC6BC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC6BC
LOAD:0000000000BEC6BC BC_DIVNV
LOAD:0000000000BEC6BC                 AND             X28, X28, #0xFF
LOAD:0000000000BEC6C0                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC6C4                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC6C8                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC6CC                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC6D0                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC6D4                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC6D8                 B.LS            loc_BEE374
LOAD:0000000000BEC6DC                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC6E0                 B.LS            loc_BEE374
LOAD:0000000000BEC6E4                 FDIV            D0, D0, D1
LOAD:0000000000BEC6E8                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC6EC                 LDR             W16, [X21],#4
LOAD:0000000000BEC6F0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC6F4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC6F8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC6FC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC700                 BR              X8
LOAD:0000000000BEC704 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC704
LOAD:0000000000BEC704 BC_MODNV
LOAD:0000000000BEC704                 AND             X28, X28, #0xFF
LOAD:0000000000BEC708                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC70C                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC710                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC714                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC718                 B.NE            loc_BEC74C
LOAD:0000000000BEC71C                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC720                 B.NE            loc_BEC74C
LOAD:0000000000BEC724                 CBZ             W1, loc_BEE374
LOAD:0000000000BEC728                 BL              sub_BEF7FC
LOAD:0000000000BEC72C                 ADD             X0, X0, X24
LOAD:0000000000BEC730                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC734
LOAD:0000000000BEC734 loc_BEC734                              ; CODE XREF: LOAD:0000000000BEC774↓j
LOAD:0000000000BEC734                 LDR             W16, [X21],#4
LOAD:0000000000BEC738                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC73C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC740                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC744                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC748                 BR              X8
LOAD:0000000000BEC74C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC74C
LOAD:0000000000BEC74C loc_BEC74C                              ; CODE XREF: LOAD:0000000000BEC718↑j
LOAD:0000000000BEC74C                                         ; LOAD:0000000000BEC720↑j
LOAD:0000000000BEC74C                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC750                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC754                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC758                 B.LS            loc_BEE374
LOAD:0000000000BEC75C                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC760                 B.LS            loc_BEE374
LOAD:0000000000BEC764                 FDIV            D2, D0, D1
LOAD:0000000000BEC768                 FRINTM          D2, D2
LOAD:0000000000BEC76C                 FMSUB           D0, D2, D1, D0
LOAD:0000000000BEC770                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC774                 B               loc_BEC734
LOAD:0000000000BEC778 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC778
LOAD:0000000000BEC778 BC_ADDVV
LOAD:0000000000BEC778                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC77C                 AND             X28, X28, #0xFF
LOAD:0000000000BEC780                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC784                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC788                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC78C                 B.NE            loc_BEC7C0
LOAD:0000000000BEC790                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC794                 B.NE            loc_BEC7C0
LOAD:0000000000BEC798                 ADDS            W0, W0, W1
LOAD:0000000000BEC79C                 B.VS            loc_BEE38C
LOAD:0000000000BEC7A0                 ADD             X0, X0, X24
LOAD:0000000000BEC7A4                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC7A8
LOAD:0000000000BEC7A8 loc_BEC7A8                              ; CODE XREF: LOAD:0000000000BEC7E0↓j
LOAD:0000000000BEC7A8                 LDR             W16, [X21],#4
LOAD:0000000000BEC7AC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC7B0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC7B4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC7B8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC7BC                 BR              X8
LOAD:0000000000BEC7C0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC7C0
LOAD:0000000000BEC7C0 loc_BEC7C0                              ; CODE XREF: LOAD:0000000000BEC78C↑j
LOAD:0000000000BEC7C0                                         ; LOAD:0000000000BEC794↑j
LOAD:0000000000BEC7C0                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC7C4                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC7C8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC7CC                 B.LS            loc_BEE38C
LOAD:0000000000BEC7D0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC7D4                 B.LS            loc_BEE38C
LOAD:0000000000BEC7D8                 FADD            D0, D0, D1
LOAD:0000000000BEC7DC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC7E0                 B               loc_BEC7A8
LOAD:0000000000BEC7E4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC7E4
LOAD:0000000000BEC7E4 BC_SUBVV
LOAD:0000000000BEC7E4                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC7E8                 AND             X28, X28, #0xFF
LOAD:0000000000BEC7EC                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC7F0                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC7F4                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC7F8                 B.NE            loc_BEC82C
LOAD:0000000000BEC7FC                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC800                 B.NE            loc_BEC82C
LOAD:0000000000BEC804                 SUBS            W0, W0, W1
LOAD:0000000000BEC808                 B.VS            loc_BEE38C
LOAD:0000000000BEC80C                 ADD             X0, X0, X24
LOAD:0000000000BEC810                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC814
LOAD:0000000000BEC814 loc_BEC814                              ; CODE XREF: LOAD:0000000000BEC84C↓j
LOAD:0000000000BEC814                 LDR             W16, [X21],#4
LOAD:0000000000BEC818                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC81C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC820                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC824                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC828                 BR              X8
LOAD:0000000000BEC82C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC82C
LOAD:0000000000BEC82C loc_BEC82C                              ; CODE XREF: LOAD:0000000000BEC7F8↑j
LOAD:0000000000BEC82C                                         ; LOAD:0000000000BEC800↑j
LOAD:0000000000BEC82C                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC830                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC834                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC838                 B.LS            loc_BEE38C
LOAD:0000000000BEC83C                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC840                 B.LS            loc_BEE38C
LOAD:0000000000BEC844                 FSUB            D0, D0, D1
LOAD:0000000000BEC848                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC84C                 B               loc_BEC814
LOAD:0000000000BEC850 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC850
LOAD:0000000000BEC850 BC_MULVV
LOAD:0000000000BEC850                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC854                 AND             X28, X28, #0xFF
LOAD:0000000000BEC858                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC85C                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC860                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC864                 B.NE            loc_BEC8A0
LOAD:0000000000BEC868                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC86C                 B.NE            loc_BEC8A0
LOAD:0000000000BEC870                 SMULL           X0, W0, W1
LOAD:0000000000BEC874                 CMP             X0, W0,SXTW
LOAD:0000000000BEC878                 MOV             W0, W0
LOAD:0000000000BEC87C                 B.NE            loc_BEE38C
LOAD:0000000000BEC880                 ADD             X0, X0, X24
LOAD:0000000000BEC884                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC888
LOAD:0000000000BEC888 loc_BEC888                              ; CODE XREF: LOAD:0000000000BEC8C0↓j
LOAD:0000000000BEC888                 LDR             W16, [X21],#4
LOAD:0000000000BEC88C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC890                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC894                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC898                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC89C                 BR              X8
LOAD:0000000000BEC8A0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC8A0
LOAD:0000000000BEC8A0 loc_BEC8A0                              ; CODE XREF: LOAD:0000000000BEC864↑j
LOAD:0000000000BEC8A0                                         ; LOAD:0000000000BEC86C↑j
LOAD:0000000000BEC8A0                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC8A4                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC8A8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC8AC                 B.LS            loc_BEE38C
LOAD:0000000000BEC8B0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC8B4                 B.LS            loc_BEE38C
LOAD:0000000000BEC8B8                 FMUL            D0, D0, D1
LOAD:0000000000BEC8BC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC8C0                 B               loc_BEC888
LOAD:0000000000BEC8C4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC8C4
LOAD:0000000000BEC8C4 BC_DIVVV
LOAD:0000000000BEC8C4                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC8C8                 AND             X28, X28, #0xFF
LOAD:0000000000BEC8CC                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC8D0                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC8D4                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC8D8                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC8DC                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC8E0                 B.LS            loc_BEE38C
LOAD:0000000000BEC8E4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC8E8                 B.LS            loc_BEE38C
LOAD:0000000000BEC8EC                 FDIV            D0, D0, D1
LOAD:0000000000BEC8F0                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC8F4                 LDR             W16, [X21],#4
LOAD:0000000000BEC8F8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC8FC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC900                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC904                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC908                 BR              X8
LOAD:0000000000BEC90C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC90C
LOAD:0000000000BEC90C BC_MODVV
LOAD:0000000000BEC90C                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC910                 AND             X28, X28, #0xFF
LOAD:0000000000BEC914                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC918                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC91C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC920                 B.NE            loc_BEC954
LOAD:0000000000BEC924                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC928                 B.NE            loc_BEC954
LOAD:0000000000BEC92C                 CBZ             W1, loc_BEE38C
LOAD:0000000000BEC930                 BL              sub_BEF7FC
LOAD:0000000000BEC934                 ADD             X0, X0, X24
LOAD:0000000000BEC938                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC93C
LOAD:0000000000BEC93C loc_BEC93C                              ; CODE XREF: LOAD:0000000000BEC97C↓j
LOAD:0000000000BEC93C                 LDR             W16, [X21],#4
LOAD:0000000000BEC940                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC944                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC948                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC94C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC950                 BR              X8
LOAD:0000000000BEC954 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC954
LOAD:0000000000BEC954 loc_BEC954                              ; CODE XREF: LOAD:0000000000BEC920↑j
LOAD:0000000000BEC954                                         ; LOAD:0000000000BEC928↑j
LOAD:0000000000BEC954                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC958                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC95C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC960                 B.LS            loc_BEE38C
LOAD:0000000000BEC964                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC968                 B.LS            loc_BEE38C
LOAD:0000000000BEC96C                 FDIV            D2, D0, D1
LOAD:0000000000BEC970                 FRINTM          D2, D2
LOAD:0000000000BEC974                 FMSUB           D0, D2, D1, D0
LOAD:0000000000BEC978                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC97C                 B               loc_BEC93C
LOAD:0000000000BEC980 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC980
LOAD:0000000000BEC980 BC_POW
LOAD:0000000000BEC980                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC984                 AND             X28, X28, #0xFF
LOAD:0000000000BEC988                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC98C                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC990                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC994                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC998                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC99C                 B.LS            loc_BEE38C
LOAD:0000000000BEC9A0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC9A4                 B.LS            loc_BEE38C
LOAD:0000000000BEC9A8                 BL              sub_62F5D0
LOAD:0000000000BEC9AC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC9B0                 LDR             W16, [X21],#4
LOAD:0000000000BEC9B4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC9B8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC9BC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC9C0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC9C4                 BR              X8
LOAD:0000000000BEC9C8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC9C8
LOAD:0000000000BEC9C8 BC_CAT
LOAD:0000000000BEC9C8                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC9CC                 AND             X28, X28, #0xFF
LOAD:0000000000BEC9D0                 STR             X19, [X23,#0x20]
LOAD:0000000000BEC9D4                 SUB             X2, X28, X17
LOAD:0000000000BEC9D8                 ADD             X1, X19, X28,LSL#3
LOAD:0000000000BEC9DC ; START OF FUNCTION CHUNK FOR sub_BEE0EC
LOAD:0000000000BEC9DC
LOAD:0000000000BEC9DC loc_BEC9DC                              ; CODE XREF: sub_BEE0EC+2C↓j
LOAD:0000000000BEC9DC                 MOV             X0, X23
LOAD:0000000000BEC9E0                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEC9E4                 BL              sub_BF4358
LOAD:0000000000BEC9E8                 LDURB           W17, [X21,#-1]
LOAD:0000000000BEC9EC                 LDR             X19, [X23,#0x20]
LOAD:0000000000BEC9F0                 CBNZ            X0, loc_BEE3B0
LOAD:0000000000BEC9F4                 LDR             X8, [X19,X17,LSL#3]
LOAD:0000000000BEC9F8                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC9FC                 LDR             W16, [X21],#4
LOAD:0000000000BECA00                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECA04                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECA08                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECA0C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECA10                 BR              X8
LOAD:0000000000BECA10 ; END OF FUNCTION CHUNK FOR sub_BEE0EC
LOAD:0000000000BECA14
LOAD:0000000000BECA14 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECA14
LOAD:0000000000BECA14
LOAD:0000000000BECA14 BC_UGET
LOAD:0000000000BECA14                 LDUR            X1, [X19,#-0x10]
LOAD:0000000000BECA18                 ADD             X28, X28, #5
LOAD:0000000000BECA1C                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BECA20                 LDR             X1, [X1,X28,LSL#3]
LOAD:0000000000BECA24                 LDR             X1, [X1,#0x20]
LOAD:0000000000BECA28                 LDR             X8, [X1]
LOAD:0000000000BECA2C                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECA30                 LDR             W16, [X21],#4
LOAD:0000000000BECA34                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECA38                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECA3C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECA40                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECA44                 BR              X8
LOAD:0000000000BECA44 ; End of function BC_UGET
LOAD:0000000000BECA44
LOAD:0000000000BECA48
LOAD:0000000000BECA48 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECA48
LOAD:0000000000BECA48
LOAD:0000000000BECA48 BC_USETV
LOAD:0000000000BECA48                 LDUR            X1, [X19,#-0x10]
LOAD:0000000000BECA4C                 ADD             X27, X27, #5
LOAD:0000000000BECA50                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BECA54                 LDR             X0, [X1,X27,LSL#3]
LOAD:0000000000BECA58                 LDR             X2, [X19,X28,LSL#3]
LOAD:0000000000BECA5C                 LDR             X1, [X0,#0x20]
LOAD:0000000000BECA60                 LDRB            W10, [X0,#8]
LOAD:0000000000BECA64                 LDRB            W8, [X0,#0xA]
LOAD:0000000000BECA68                 ASR             X15, X2, #0x2F ; '/'
LOAD:0000000000BECA6C                 STR             X2, [X1]
LOAD:0000000000BECA70                 ADD             X15, X15, #4
LOAD:0000000000BECA74                 TST             W10, #4
LOAD:0000000000BECA78                 CCMP            W8, #0, #4, NE
LOAD:0000000000BECA7C                 CCMN            X15, #0xA, #0, NE
LOAD:0000000000BECA80                 B.HI            loc_BECA9C
LOAD:0000000000BECA84
LOAD:0000000000BECA84 loc_BECA84                              ; CODE XREF: BC_USETV+60↓j
LOAD:0000000000BECA84                                         ; BC_USETV+6C↓j
LOAD:0000000000BECA84                 LDR             W16, [X21],#4
LOAD:0000000000BECA88                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECA8C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECA90                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECA94                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECA98                 BR              X8
LOAD:0000000000BECA9C ; ---------------------------------------------------------------------------
LOAD:0000000000BECA9C
LOAD:0000000000BECA9C loc_BECA9C                              ; CODE XREF: BC_USETV+38↑j
LOAD:0000000000BECA9C                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BECAA0                 LDRB            W9, [X2,#8]
LOAD:0000000000BECAA4                 TST             W9, #3
LOAD:0000000000BECAA8                 B.EQ            loc_BECA84
LOAD:0000000000BECAAC                 MOV             X0, X22
LOAD:0000000000BECAB0                 BL              sub_BF0B18
LOAD:0000000000BECAB4                 B               loc_BECA84
LOAD:0000000000BECAB4 ; End of function BC_USETV
LOAD:0000000000BECAB4
LOAD:0000000000BECAB8
LOAD:0000000000BECAB8 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECAB8
LOAD:0000000000BECAB8
LOAD:0000000000BECAB8 BC_USETS
LOAD:0000000000BECAB8                 LDUR            X1, [X19,#-0x10]
LOAD:0000000000BECABC                 ADD             X27, X27, #5
LOAD:0000000000BECAC0                 MVN             X28, X28
LOAD:0000000000BECAC4                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BECAC8                 LDR             X0, [X1,X27,LSL#3]
LOAD:0000000000BECACC                 LDR             X2, [X20,X28,LSL#3]
LOAD:0000000000BECAD0                 MOV             X8, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BECAD4                 LDR             X1, [X0,#0x20]
LOAD:0000000000BECAD8                 LDRB            W10, [X0,#8]
LOAD:0000000000BECADC                 ADD             X8, X2, X8,LSL#47
LOAD:0000000000BECAE0                 LDRB            W9, [X2,#8]
LOAD:0000000000BECAE4                 STR             X8, [X1]
LOAD:0000000000BECAE8                 TBNZ            W10, #2, loc_BECB04
LOAD:0000000000BECAEC
LOAD:0000000000BECAEC loc_BECAEC                              ; CODE XREF: BC_USETS+58↓j
LOAD:0000000000BECAEC                                         ; BC_USETS+64↓j
LOAD:0000000000BECAEC                 LDR             W16, [X21],#4
LOAD:0000000000BECAF0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECAF4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECAF8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECAFC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECB00                 BR              X8
LOAD:0000000000BECB04 ; ---------------------------------------------------------------------------
LOAD:0000000000BECB04
LOAD:0000000000BECB04 loc_BECB04                              ; CODE XREF: BC_USETS+30↑j
LOAD:0000000000BECB04                 LDRB            W8, [X0,#0xA]
LOAD:0000000000BECB08                 TST             W9, #3
LOAD:0000000000BECB0C                 CCMP            W8, #0, #4, NE
LOAD:0000000000BECB10                 B.EQ            loc_BECAEC
LOAD:0000000000BECB14                 MOV             X0, X22
LOAD:0000000000BECB18                 BL              sub_BF0B18
LOAD:0000000000BECB1C                 B               loc_BECAEC
LOAD:0000000000BECB1C ; End of function BC_USETS
LOAD:0000000000BECB1C
LOAD:0000000000BECB20
LOAD:0000000000BECB20 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECB20
LOAD:0000000000BECB20
LOAD:0000000000BECB20 BC_USETN
LOAD:0000000000BECB20                 LDUR            X1, [X19,#-0x10]
LOAD:0000000000BECB24                 ADD             X27, X27, #5
LOAD:0000000000BECB28                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BECB2C                 LDR             X1, [X1,X27,LSL#3]
LOAD:0000000000BECB30                 LDR             X8, [X20,X28,LSL#3]
LOAD:0000000000BECB34                 LDR             X1, [X1,#0x20]
LOAD:0000000000BECB38                 STR             X8, [X1]
LOAD:0000000000BECB3C                 LDR             W16, [X21],#4
LOAD:0000000000BECB40                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECB44                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECB48                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECB4C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECB50                 BR              X8
LOAD:0000000000BECB50 ; End of function BC_USETN
LOAD:0000000000BECB50
LOAD:0000000000BECB54
LOAD:0000000000BECB54 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECB54
LOAD:0000000000BECB54
LOAD:0000000000BECB54 BC_USETP
LOAD:0000000000BECB54                 LDUR            X1, [X19,#-0x10]
LOAD:0000000000BECB58                 ADD             X27, X27, #5
LOAD:0000000000BECB5C                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BECB60                 LDR             X1, [X1,X27,LSL#3]
LOAD:0000000000BECB64                 MVN             X8, X28,LSL#47
LOAD:0000000000BECB68                 LDR             X1, [X1,#0x20]
LOAD:0000000000BECB6C                 STR             X8, [X1]
LOAD:0000000000BECB70                 LDR             W16, [X21],#4
LOAD:0000000000BECB74                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECB78                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECB7C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECB80                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECB84                 BR              X8
LOAD:0000000000BECB84 ; End of function BC_USETP
LOAD:0000000000BECB84
LOAD:0000000000BECB88
LOAD:0000000000BECB88 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECB88
LOAD:0000000000BECB88
LOAD:0000000000BECB88 BC_UCLO
LOAD:0000000000BECB88                 LDR             X2, [X23,#0x40]
LOAD:0000000000BECB8C                 ADD             X28, X21, X28,LSL#2
LOAD:0000000000BECB90                 STR             X19, [X23,#0x20]
LOAD:0000000000BECB94                 SUB             X21, X28, #0x20,LSL#12 ; ' '
LOAD:0000000000BECB98                 CBZ             X2, loc_BECBAC
LOAD:0000000000BECB9C                 MOV             X0, X23
LOAD:0000000000BECBA0                 ADD             X1, X19, X27,LSL#3
LOAD:0000000000BECBA4                 BL              sub_BCA6AC
LOAD:0000000000BECBA8                 LDR             X19, [X23,#0x20]
LOAD:0000000000BECBAC
LOAD:0000000000BECBAC loc_BECBAC                              ; CODE XREF: BC_UCLO+10↑j
LOAD:0000000000BECBAC                 LDR             W16, [X21],#4
LOAD:0000000000BECBB0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECBB4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECBB8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECBBC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECBC0                 BR              X8
LOAD:0000000000BECBC0 ; End of function BC_UCLO
LOAD:0000000000BECBC0
LOAD:0000000000BECBC4
LOAD:0000000000BECBC4 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECBC4
LOAD:0000000000BECBC4
LOAD:0000000000BECBC4 BC_FNEW
LOAD:0000000000BECBC4
LOAD:0000000000BECBC4 arg_8           =  8
LOAD:0000000000BECBC4
LOAD:0000000000BECBC4                 MVN             X28, X28
LOAD:0000000000BECBC8                 STR             X19, [X23,#0x20]
LOAD:0000000000BECBCC                 LDUR            X2, [X19,#-0x10]
LOAD:0000000000BECBD0                 STR             X21, [SP,#arg_8]
LOAD:0000000000BECBD4                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BECBD8                 MOV             X0, X23
LOAD:0000000000BECBDC                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BECBE0                 BL              sub_BCA8CC
LOAD:0000000000BECBE4                 LDR             X19, [X23,#0x20]
LOAD:0000000000BECBE8                 MOV             X8, #0xFFFFFFFFFFFFFFF7
LOAD:0000000000BECBEC                 ADD             X0, X0, X8,LSL#47
LOAD:0000000000BECBF0                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BECBF4                 LDR             W16, [X21],#4
LOAD:0000000000BECBF8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECBFC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECC00                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECC04                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECC08                 BR              X8
LOAD:0000000000BECC08 ; End of function BC_FNEW
LOAD:0000000000BECC08
LOAD:0000000000BECC0C
LOAD:0000000000BECC0C ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECC0C
LOAD:0000000000BECC0C
LOAD:0000000000BECC0C BC_KSTR
LOAD:0000000000BECC0C                 MVN             X28, X28
LOAD:0000000000BECC10                 LDR             X8, [X20,X28,LSL#3]
LOAD:0000000000BECC14                 MOV             X9, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BECC18                 ADD             X8, X8, X9,LSL#47
LOAD:0000000000BECC1C                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECC20                 LDR             W16, [X21],#4
LOAD:0000000000BECC24                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECC28                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECC2C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECC30                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECC34                 BR              X8
LOAD:0000000000BECC34 ; End of function BC_KSTR
LOAD:0000000000BECC34
LOAD:0000000000BECC38
LOAD:0000000000BECC38 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECC38
LOAD:0000000000BECC38
LOAD:0000000000BECC38 BC_KCDATA
LOAD:0000000000BECC38                 MVN             X28, X28
LOAD:0000000000BECC3C                 LDR             X8, [X20,X28,LSL#3]
LOAD:0000000000BECC40                 MOV             X9, #0xFFFFFFFFFFFFFFF5
LOAD:0000000000BECC44                 ADD             X8, X8, X9,LSL#47
LOAD:0000000000BECC48                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECC4C                 LDR             W16, [X21],#4
LOAD:0000000000BECC50                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECC54                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECC58                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECC5C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECC60                 BR              X8
LOAD:0000000000BECC60 ; End of function BC_KCDATA
LOAD:0000000000BECC60
LOAD:0000000000BECC64
LOAD:0000000000BECC64 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECC64
LOAD:0000000000BECC64
LOAD:0000000000BECC64 BC_KSHORT
LOAD:0000000000BECC64                 SXTH            W28, W28
LOAD:0000000000BECC68                 ADD             X8, X28, X24
LOAD:0000000000BECC6C                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECC70                 LDR             W16, [X21],#4
LOAD:0000000000BECC74                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECC78                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECC7C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECC80                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECC84                 BR              X8
LOAD:0000000000BECC84 ; End of function BC_KSHORT
LOAD:0000000000BECC84
LOAD:0000000000BECC88
LOAD:0000000000BECC88 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECC88
LOAD:0000000000BECC88
LOAD:0000000000BECC88 BC_KNUM
LOAD:0000000000BECC88                 LDR             X8, [X20,X28,LSL#3]
LOAD:0000000000BECC8C                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECC90                 LDR             W16, [X21],#4
LOAD:0000000000BECC94                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECC98                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECC9C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECCA0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECCA4                 BR              X8
LOAD:0000000000BECCA4 ; End of function BC_KNUM
LOAD:0000000000BECCA4
LOAD:0000000000BECCA8
LOAD:0000000000BECCA8 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECCA8
LOAD:0000000000BECCA8
LOAD:0000000000BECCA8 BC_KPRI
LOAD:0000000000BECCA8                 MVN             X8, X28,LSL#47
LOAD:0000000000BECCAC                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECCB0                 LDR             W16, [X21],#4
LOAD:0000000000BECCB4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECCB8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECCBC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECCC0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECCC4                 BR              X8
LOAD:0000000000BECCC4 ; End of function BC_KPRI
LOAD:0000000000BECCC4
LOAD:0000000000BECCC8
LOAD:0000000000BECCC8 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECCC8
LOAD:0000000000BECCC8
LOAD:0000000000BECCC8 BC_KNIL
LOAD:0000000000BECCC8                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BECCCC                 ADD             X28, X19, X28,LSL#3
LOAD:0000000000BECCD0                 STR             X26, [X27],#8
LOAD:0000000000BECCD4
LOAD:0000000000BECCD4 loc_BECCD4                              ; CODE XREF: BC_KNIL+14↓j
LOAD:0000000000BECCD4                 CMP             X27, X28
LOAD:0000000000BECCD8                 STR             X26, [X27],#8
LOAD:0000000000BECCDC                 B.LT            loc_BECCD4
LOAD:0000000000BECCE0                 LDR             W16, [X21],#4
LOAD:0000000000BECCE4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECCE8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECCEC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECCF0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECCF4                 BR              X8
LOAD:0000000000BECCF4 ; End of function BC_KNIL
LOAD:0000000000BECCF4
LOAD:0000000000BECCF8
LOAD:0000000000BECCF8 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECCF8
LOAD:0000000000BECCF8
LOAD:0000000000BECCF8 BC_TNEW
LOAD:0000000000BECCF8
LOAD:0000000000BECCF8 arg_8           =  8
LOAD:0000000000BECCF8
LOAD:0000000000BECCF8                 LDP             X2, X3, [X22,#0x10]
LOAD:0000000000BECCFC                 STR             X19, [X23,#0x20]
LOAD:0000000000BECD00                 STR             X21, [SP,#arg_8]
LOAD:0000000000BECD04                 MOV             X0, X23
LOAD:0000000000BECD08                 CMP             X2, X3
LOAD:0000000000BECD0C                 B.CS            loc_BECD4C
LOAD:0000000000BECD10
LOAD:0000000000BECD10 loc_BECD10                              ; CODE XREF: BC_TNEW+5C↓j
LOAD:0000000000BECD10                 AND             X1, X28, #0x7FF
LOAD:0000000000BECD14                 LSR             X2, X28, #0xB
LOAD:0000000000BECD18                 CMP             X1, #0x7FF
LOAD:0000000000BECD1C                 MOV             W8, #0x801
LOAD:0000000000BECD20                 CSEL            X1, X1, X8, NE
LOAD:0000000000BECD24                 BL              sub_BF2044
LOAD:0000000000BECD28                 LDR             X19, [X23,#0x20]
LOAD:0000000000BECD2C                 MOVK            X0, #0xFFFA,LSL#48
LOAD:0000000000BECD30                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BECD34                 LDR             W16, [X21],#4
LOAD:0000000000BECD38                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECD3C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECD40                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECD44                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECD48                 BR              X8
LOAD:0000000000BECD4C ; ---------------------------------------------------------------------------
LOAD:0000000000BECD4C
LOAD:0000000000BECD4C loc_BECD4C                              ; CODE XREF: BC_TNEW+14↑j
LOAD:0000000000BECD4C                 BL              sub_BF06C0
LOAD:0000000000BECD50                 MOV             X0, X23
LOAD:0000000000BECD54                 B               loc_BECD10
LOAD:0000000000BECD54 ; End of function BC_TNEW
LOAD:0000000000BECD54
LOAD:0000000000BECD58
LOAD:0000000000BECD58 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECD58
LOAD:0000000000BECD58
LOAD:0000000000BECD58 BC_TDUP
LOAD:0000000000BECD58
LOAD:0000000000BECD58 arg_8           =  8
LOAD:0000000000BECD58
LOAD:0000000000BECD58                 LDP             X2, X3, [X22,#0x10]
LOAD:0000000000BECD5C                 STR             X19, [X23,#0x20]
LOAD:0000000000BECD60                 STR             X21, [SP,#arg_8]
LOAD:0000000000BECD64                 MOV             X0, X23
LOAD:0000000000BECD68                 CMP             X2, X3
LOAD:0000000000BECD6C                 B.CS            loc_BECDA0
LOAD:0000000000BECD70
LOAD:0000000000BECD70 loc_BECD70                              ; CODE XREF: BC_TDUP+50↓j
LOAD:0000000000BECD70                 MVN             X28, X28
LOAD:0000000000BECD74                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BECD78                 BL              sub_BF22CC
LOAD:0000000000BECD7C                 LDR             X19, [X23,#0x20]
LOAD:0000000000BECD80                 MOVK            X0, #0xFFFA,LSL#48
LOAD:0000000000BECD84                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BECD88                 LDR             W16, [X21],#4
LOAD:0000000000BECD8C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECD90                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECD94                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECD98                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECD9C                 BR              X8
LOAD:0000000000BECDA0 ; ---------------------------------------------------------------------------
LOAD:0000000000BECDA0
LOAD:0000000000BECDA0 loc_BECDA0                              ; CODE XREF: BC_TDUP+14↑j
LOAD:0000000000BECDA0                 BL              sub_BF06C0
LOAD:0000000000BECDA4                 MOV             X0, X23
LOAD:0000000000BECDA8                 B               loc_BECD70
LOAD:0000000000BECDA8 ; End of function BC_TDUP
LOAD:0000000000BECDA8
LOAD:0000000000BECDAC
LOAD:0000000000BECDAC ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECDAC
LOAD:0000000000BECDAC
LOAD:0000000000BECDAC BC_GGET
LOAD:0000000000BECDAC
LOAD:0000000000BECDAC arg_8           =  8
LOAD:0000000000BECDAC arg_18          =  0x18
LOAD:0000000000BECDAC
LOAD:0000000000BECDAC ; FUNCTION CHUNK AT LOAD:0000000000BED084 SIZE 000000D8 BYTES
LOAD:0000000000BECDAC ; FUNCTION CHUNK AT LOAD:0000000000BEE124 SIZE 00000028 BYTES
LOAD:0000000000BECDAC ; FUNCTION CHUNK AT LOAD:0000000000BEE160 SIZE 0000005C BYTES
LOAD:0000000000BECDAC ; FUNCTION CHUNK AT LOAD:0000000000BEE1E4 SIZE 00000018 BYTES
LOAD:0000000000BECDAC ; FUNCTION CHUNK AT LOAD:0000000000BEE218 SIZE 00000058 BYTES
LOAD:0000000000BECDAC
LOAD:0000000000BECDAC                 LDUR            X0, [X19,#-0x10]
LOAD:0000000000BECDB0                 MVN             X28, X28
LOAD:0000000000BECDB4                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BECDB8                 LDR             X1, [X0,#0x10]
LOAD:0000000000BECDBC                 LDR             X28, [X20,X28,LSL#3]
LOAD:0000000000BECDC0                 B               BC_TGETS_Z
LOAD:0000000000BECDC4 ; ---------------------------------------------------------------------------
LOAD:0000000000BECDC4
LOAD:0000000000BECDC4 BC_GSET
LOAD:0000000000BECDC4                 LDUR            X0, [X19,#-0x10]
LOAD:0000000000BECDC8                 MVN             X28, X28
LOAD:0000000000BECDCC                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BECDD0                 LDR             X1, [X0,#0x10]
LOAD:0000000000BECDD4                 LDR             X28, [X20,X28,LSL#3]
LOAD:0000000000BECDD8                 B               BC_TSETS_Z
LOAD:0000000000BECDDC ; ---------------------------------------------------------------------------
LOAD:0000000000BECDDC
LOAD:0000000000BECDDC BC_TGETV
LOAD:0000000000BECDDC                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BECDE0                 AND             X28, X28, #0xFF
LOAD:0000000000BECDE4                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BECDE8                 LDR             X9, [X19,X28,LSL#3]
LOAD:0000000000BECDEC                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BECDF0                 CMN             X15, #0xC
LOAD:0000000000BECDF4                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BECDF8                 B.NE            loc_BEE160
LOAD:0000000000BECDFC                 CMP             X25, X9,LSR#32
LOAD:0000000000BECE00                 B.NE            loc_BECE54
LOAD:0000000000BECE04                 LDR             X2, [X1,#0x10]
LOAD:0000000000BECE08                 LDR             W0, [X1,#0x30]
LOAD:0000000000BECE0C                 ADD             X2, X2, W9,UXTW#3
LOAD:0000000000BECE10                 CMP             W9, W0
LOAD:0000000000BECE14                 B.CS            loc_BEE160
LOAD:0000000000BECE18                 LDR             X8, [X2]
LOAD:0000000000BECE1C                 CMP             X8, X26
LOAD:0000000000BECE20                 B.EQ            loc_BECE40
LOAD:0000000000BECE24
LOAD:0000000000BECE24 loc_BECE24                              ; CODE XREF: BC_GGET+98↓j
LOAD:0000000000BECE24                                         ; BC_GGET+A0↓j
LOAD:0000000000BECE24                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECE28                 LDR             W16, [X21],#4
LOAD:0000000000BECE2C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECE30                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECE34                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECE38                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECE3C                 BR              X8
LOAD:0000000000BECE40 ; ---------------------------------------------------------------------------
LOAD:0000000000BECE40
LOAD:0000000000BECE40 loc_BECE40                              ; CODE XREF: BC_GGET+74↑j
LOAD:0000000000BECE40                 LDR             X0, [X1,#0x20]
LOAD:0000000000BECE44                 CBZ             X0, loc_BECE24
LOAD:0000000000BECE48                 LDRB            W9, [X0,#0xA]
LOAD:0000000000BECE4C                 TBNZ            W9, #0, loc_BECE24
LOAD:0000000000BECE50                 B               loc_BEE160
LOAD:0000000000BECE54 ; ---------------------------------------------------------------------------
LOAD:0000000000BECE54
LOAD:0000000000BECE54 loc_BECE54                              ; CODE XREF: BC_GGET+54↑j
LOAD:0000000000BECE54                 ASR             X15, X9, #0x2F ; '/'
LOAD:0000000000BECE58                 CMN             X15, #5
LOAD:0000000000BECE5C                 B.NE            loc_BEE160
LOAD:0000000000BECE60                 AND             X28, X9, #0x7FFFFFFFFFFF
LOAD:0000000000BECE64                 B               BC_TGETS_Z
LOAD:0000000000BECE68 ; ---------------------------------------------------------------------------
LOAD:0000000000BECE68
LOAD:0000000000BECE68 BC_TGETS
LOAD:0000000000BECE68                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BECE6C                 AND             X28, X28, #0xFF
LOAD:0000000000BECE70                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BECE74                 MVN             X28, X28
LOAD:0000000000BECE78                 LDR             X28, [X20,X28,LSL#3]
LOAD:0000000000BECE7C                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BECE80                 CMN             X15, #0xC
LOAD:0000000000BECE84                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BECE88                 B.NE            loc_BEE124
LOAD:0000000000BECE8C
LOAD:0000000000BECE8C BC_TGETS_Z                              ; CODE XREF: BC_GGET+14↑j
LOAD:0000000000BECE8C                                         ; BC_GGET+B8↑j
LOAD:0000000000BECE8C                 LDR             W9, [X1,#0x34]
LOAD:0000000000BECE90                 LDR             W10, [X28,#0xC]
LOAD:0000000000BECE94                 LDR             X2, [X1,#0x28]
LOAD:0000000000BECE98                 AND             W9, W9, W10
LOAD:0000000000BECE9C                 ADD             X9, X9, X9,LSL#1
LOAD:0000000000BECEA0                 MOV             X3, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BECEA4                 ADD             X2, X2, X9,LSL#3
LOAD:0000000000BECEA8                 ADD             X3, X28, X3,LSL#47
LOAD:0000000000BECEAC
LOAD:0000000000BECEAC loc_BECEAC                              ; CODE XREF: BC_GGET:loc_BECEE0↓j
LOAD:0000000000BECEAC                 LDP             X8, X0, [X2]
LOAD:0000000000BECEB0                 LDR             X2, [X2,#0x10]
LOAD:0000000000BECEB4                 CMP             X0, X3
LOAD:0000000000BECEB8                 B.NE            loc_BECEE0
LOAD:0000000000BECEBC                 CMP             X8, X26
LOAD:0000000000BECEC0                 B.EQ            loc_BECEE8
LOAD:0000000000BECEC4
LOAD:0000000000BECEC4 loc_BECEC4                              ; CODE XREF: BC_GGET+140↓j
LOAD:0000000000BECEC4                                         ; BC_GGET+148↓j
LOAD:0000000000BECEC4                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECEC8                 LDR             W16, [X21],#4
LOAD:0000000000BECECC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECED0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECED4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECED8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECEDC                 BR              X8
LOAD:0000000000BECEE0 ; ---------------------------------------------------------------------------
LOAD:0000000000BECEE0
LOAD:0000000000BECEE0 loc_BECEE0                              ; CODE XREF: BC_GGET+10C↑j
LOAD:0000000000BECEE0                 CBNZ            X2, loc_BECEAC
LOAD:0000000000BECEE4                 MOV             X8, X26
LOAD:0000000000BECEE8
LOAD:0000000000BECEE8 loc_BECEE8                              ; CODE XREF: BC_GGET+114↑j
LOAD:0000000000BECEE8                 LDR             X0, [X1,#0x20]
LOAD:0000000000BECEEC                 CBZ             X0, loc_BECEC4
LOAD:0000000000BECEF0                 LDRB            W9, [X0,#0xA]
LOAD:0000000000BECEF4                 TBNZ            W9, #0, loc_BECEC4
LOAD:0000000000BECEF8                 B               loc_BEE134
LOAD:0000000000BECEF8 ; End of function BC_GGET
LOAD:0000000000BECEF8
LOAD:0000000000BECEFC
LOAD:0000000000BECEFC ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECEFC
LOAD:0000000000BECEFC
LOAD:0000000000BECEFC BC_TGETB
LOAD:0000000000BECEFC
LOAD:0000000000BECEFC arg_18          =  0x18
LOAD:0000000000BECEFC
LOAD:0000000000BECEFC ; FUNCTION CHUNK AT LOAD:0000000000BEE14C SIZE 00000014 BYTES
LOAD:0000000000BECEFC
LOAD:0000000000BECEFC                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BECF00                 AND             X28, X28, #0xFF
LOAD:0000000000BECF04                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BECF08                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BECF0C                 CMN             X15, #0xC
LOAD:0000000000BECF10                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BECF14                 B.NE            loc_BEE14C
LOAD:0000000000BECF18                 LDR             X2, [X1,#0x10]
LOAD:0000000000BECF1C                 LDR             W0, [X1,#0x30]
LOAD:0000000000BECF20                 ADD             X2, X2, X28,LSL#3
LOAD:0000000000BECF24                 CMP             W28, W0
LOAD:0000000000BECF28                 B.CS            loc_BEE14C
LOAD:0000000000BECF2C                 LDR             X8, [X2]
LOAD:0000000000BECF30                 CMP             X8, X26
LOAD:0000000000BECF34                 B.EQ            loc_BECF54
LOAD:0000000000BECF38
LOAD:0000000000BECF38 loc_BECF38                              ; CODE XREF: BC_TGETB+5C↓j
LOAD:0000000000BECF38                                         ; BC_TGETB+64↓j
LOAD:0000000000BECF38                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECF3C                 LDR             W16, [X21],#4
LOAD:0000000000BECF40                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECF44                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECF48                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECF4C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECF50                 BR              X8
LOAD:0000000000BECF54 ; ---------------------------------------------------------------------------
LOAD:0000000000BECF54
LOAD:0000000000BECF54 loc_BECF54                              ; CODE XREF: BC_TGETB+38↑j
LOAD:0000000000BECF54                 LDR             X0, [X1,#0x20]
LOAD:0000000000BECF58                 CBZ             X0, loc_BECF38
LOAD:0000000000BECF5C                 LDRB            W9, [X0,#0xA]
LOAD:0000000000BECF60                 TBNZ            W9, #0, loc_BECF38
LOAD:0000000000BECF64                 B               loc_BEE14C
LOAD:0000000000BECF64 ; End of function BC_TGETB
LOAD:0000000000BECF64
LOAD:0000000000BECF68
LOAD:0000000000BECF68 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECF68
LOAD:0000000000BECF68
LOAD:0000000000BECF68 BC_TGETR
LOAD:0000000000BECF68
LOAD:0000000000BECF68 ; FUNCTION CHUNK AT LOAD:0000000000BEE1BC SIZE 00000018 BYTES
LOAD:0000000000BECF68
LOAD:0000000000BECF68                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BECF6C                 AND             X28, X28, #0xFF
LOAD:0000000000BECF70                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BECF74                 LDR             X9, [X19,X28,LSL#3]
LOAD:0000000000BECF78                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BECF7C                 LDR             X2, [X0,#0x10]
LOAD:0000000000BECF80                 LDR             W10, [X0,#0x30]
LOAD:0000000000BECF84                 ADD             X2, X2, W9,UXTW#3
LOAD:0000000000BECF88                 CMP             W9, W10
LOAD:0000000000BECF8C                 B.CS            loc_BEE1BC
LOAD:0000000000BECF90                 LDR             X8, [X2]
LOAD:0000000000BECF94
LOAD:0000000000BECF94 loc_BECF94                              ; CODE XREF: BC_TGETR+1260↓j
LOAD:0000000000BECF94                                         ; BC_TGETR+1268↓j
LOAD:0000000000BECF94                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECF98                 LDR             W16, [X21],#4
LOAD:0000000000BECF9C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BECFA0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BECFA4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BECFA8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BECFAC                 BR              X8
LOAD:0000000000BECFAC ; End of function BC_TGETR
LOAD:0000000000BECFAC
LOAD:0000000000BECFB0
LOAD:0000000000BECFB0 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BECFB0
LOAD:0000000000BECFB0
LOAD:0000000000BECFB0 BC_TSETV
LOAD:0000000000BECFB0
LOAD:0000000000BECFB0 ; FUNCTION CHUNK AT LOAD:0000000000BED084 SIZE 000000D8 BYTES
LOAD:0000000000BECFB0 ; FUNCTION CHUNK AT LOAD:0000000000BEE210 SIZE 00000008 BYTES
LOAD:0000000000BECFB0
LOAD:0000000000BECFB0                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BECFB4                 AND             X28, X28, #0xFF
LOAD:0000000000BECFB8                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BECFBC                 LDR             X9, [X19,X28,LSL#3]
LOAD:0000000000BECFC0                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BECFC4                 CMN             X15, #0xC
LOAD:0000000000BECFC8                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BECFCC                 B.NE            loc_BEE210
LOAD:0000000000BECFD0                 CMP             X25, X9,LSR#32
LOAD:0000000000BECFD4                 B.NE            loc_BED04C
LOAD:0000000000BECFD8                 LDR             X2, [X1,#0x10]
LOAD:0000000000BECFDC                 LDR             W0, [X1,#0x30]
LOAD:0000000000BECFE0                 ADD             X2, X2, W9,UXTW#3
LOAD:0000000000BECFE4                 CMP             W9, W0
LOAD:0000000000BECFE8                 B.CS            loc_BEE210
LOAD:0000000000BECFEC                 LDR             X9, [X2]
LOAD:0000000000BECFF0                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BECFF4                 LDRB            W10, [X1,#8]
LOAD:0000000000BECFF8                 CMP             X9, X26
LOAD:0000000000BECFFC                 B.EQ            loc_BED020
LOAD:0000000000BED000
LOAD:0000000000BED000 loc_BED000                              ; CODE XREF: BC_TSETV+74↓j
LOAD:0000000000BED000                                         ; BC_TSETV+7C↓j
LOAD:0000000000BED000                 STR             X8, [X2]
LOAD:0000000000BED004                 TBNZ            W10, #2, loc_BED034
LOAD:0000000000BED008
LOAD:0000000000BED008 loc_BED008                              ; CODE XREF: BC_TSETV+98↓j
LOAD:0000000000BED008                 LDR             W16, [X21],#4
LOAD:0000000000BED00C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED010                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED014                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED018                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED01C                 BR              X8
LOAD:0000000000BED020 ; ---------------------------------------------------------------------------
LOAD:0000000000BED020
LOAD:0000000000BED020 loc_BED020                              ; CODE XREF: BC_TSETV+4C↑j
LOAD:0000000000BED020                 LDR             X0, [X1,#0x20]
LOAD:0000000000BED024                 CBZ             X0, loc_BED000
LOAD:0000000000BED028                 LDRB            W9, [X0,#0xA]
LOAD:0000000000BED02C                 TBNZ            W9, #1, loc_BED000
LOAD:0000000000BED030                 B               loc_BEE210
LOAD:0000000000BED034 ; ---------------------------------------------------------------------------
LOAD:0000000000BED034
LOAD:0000000000BED034 loc_BED034                              ; CODE XREF: BC_TSETV+54↑j
LOAD:0000000000BED034                 LDR             X9, [X22,#0x40]
LOAD:0000000000BED038                 AND             W10, W10, #0xFFFFFFFB
LOAD:0000000000BED03C                 STR             X1, [X22,#0x40]
LOAD:0000000000BED040                 STRB            W10, [X1,#8]
LOAD:0000000000BED044                 STR             X9, [X1,#0x18]
LOAD:0000000000BED048                 B               loc_BED008
LOAD:0000000000BED04C ; ---------------------------------------------------------------------------
LOAD:0000000000BED04C
LOAD:0000000000BED04C loc_BED04C                              ; CODE XREF: BC_TSETV+24↑j
LOAD:0000000000BED04C                 ASR             X15, X9, #0x2F ; '/'
LOAD:0000000000BED050                 CMN             X15, #5
LOAD:0000000000BED054                 B.NE            loc_BEE210
LOAD:0000000000BED058                 AND             X28, X9, #0x7FFFFFFFFFFF
LOAD:0000000000BED05C                 B               BC_TSETS_Z
LOAD:0000000000BED05C ; End of function BC_TSETV
LOAD:0000000000BED05C
LOAD:0000000000BED060
LOAD:0000000000BED060 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED060
LOAD:0000000000BED060
LOAD:0000000000BED060 BC_TSETS
LOAD:0000000000BED060
LOAD:0000000000BED060 ; FUNCTION CHUNK AT LOAD:0000000000BEE1D4 SIZE 00000010 BYTES
LOAD:0000000000BED060
LOAD:0000000000BED060                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BED064                 AND             X28, X28, #0xFF
LOAD:0000000000BED068                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BED06C                 MVN             X28, X28
LOAD:0000000000BED070                 LDR             X28, [X20,X28,LSL#3]
LOAD:0000000000BED074                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BED078                 CMN             X15, #0xC
LOAD:0000000000BED07C                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BED080                 B.NE            loc_BEE1D4
LOAD:0000000000BED080 ; End of function BC_TSETS
LOAD:0000000000BED080
LOAD:0000000000BED084 ; START OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BED084 ;   ADDITIONAL PARENT FUNCTION BC_TSETV
LOAD:0000000000BED084
LOAD:0000000000BED084 BC_TSETS_Z                              ; CODE XREF: BC_GGET+2C↑j
LOAD:0000000000BED084                                         ; BC_TSETV+AC↑j
LOAD:0000000000BED084                 LDR             W9, [X1,#0x34]
LOAD:0000000000BED088                 LDR             W10, [X28,#0xC]
LOAD:0000000000BED08C                 LDR             X2, [X1,#0x28]
LOAD:0000000000BED090                 AND             W9, W9, W10
LOAD:0000000000BED094                 ADD             X9, X9, X9,LSL#1
LOAD:0000000000BED098                 MOV             X3, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BED09C                 ADD             X2, X2, X9,LSL#3
LOAD:0000000000BED0A0                 ADD             X3, X28, X3,LSL#47
LOAD:0000000000BED0A4                 STRB            WZR, [X1,#0xA]
LOAD:0000000000BED0A8
LOAD:0000000000BED0A8 loc_BED0A8                              ; CODE XREF: BC_GGET+354↓j
LOAD:0000000000BED0A8                 LDP             X9, X0, [X2]
LOAD:0000000000BED0AC                 LDR             X11, [X2,#0x10]
LOAD:0000000000BED0B0                 LDRB            W10, [X1,#8]
LOAD:0000000000BED0B4                 CMP             X0, X3
LOAD:0000000000BED0B8                 B.NE            loc_BED0FC
LOAD:0000000000BED0BC                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BED0C0                 CMP             X9, X26
LOAD:0000000000BED0C4                 B.EQ            loc_BED0E8
LOAD:0000000000BED0C8
LOAD:0000000000BED0C8 loc_BED0C8                              ; CODE XREF: BC_GGET+340↓j
LOAD:0000000000BED0C8                                         ; BC_GGET+348↓j
LOAD:0000000000BED0C8                 STR             X8, [X2]
LOAD:0000000000BED0CC                 TBNZ            W10, #2, loc_BED144
LOAD:0000000000BED0D0
LOAD:0000000000BED0D0 loc_BED0D0                              ; CODE XREF: BC_GGET+394↓j
LOAD:0000000000BED0D0                                         ; BC_GGET+3AC↓j
LOAD:0000000000BED0D0                 LDR             W16, [X21],#4
LOAD:0000000000BED0D4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED0D8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED0DC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED0E0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED0E4                 BR              X8
LOAD:0000000000BED0E8 ; ---------------------------------------------------------------------------
LOAD:0000000000BED0E8
LOAD:0000000000BED0E8 loc_BED0E8                              ; CODE XREF: BC_GGET+318↑j
LOAD:0000000000BED0E8                 LDR             X0, [X1,#0x20]
LOAD:0000000000BED0EC                 CBZ             X0, loc_BED0C8
LOAD:0000000000BED0F0                 LDRB            W9, [X0,#0xA]
LOAD:0000000000BED0F4                 TBNZ            W9, #1, loc_BED0C8
LOAD:0000000000BED0F8                 B               loc_BEE1E4
LOAD:0000000000BED0FC ; ---------------------------------------------------------------------------
LOAD:0000000000BED0FC
LOAD:0000000000BED0FC loc_BED0FC                              ; CODE XREF: BC_GGET+30C↑j
LOAD:0000000000BED0FC                 MOV             X2, X11
LOAD:0000000000BED100                 CBNZ            X11, loc_BED0A8
LOAD:0000000000BED104                 LDR             X0, [X1,#0x20]
LOAD:0000000000BED108                 CBZ             X0, loc_BED114
LOAD:0000000000BED10C                 LDRB            W9, [X0,#0xA]
LOAD:0000000000BED110                 TBZ             W9, #1, loc_BEE1E4
LOAD:0000000000BED114
LOAD:0000000000BED114 loc_BED114                              ; CODE XREF: BC_GGET+35C↑j
LOAD:0000000000BED114                 MOV             X9, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BED118                 STR             X21, [SP,#arg_8]
LOAD:0000000000BED11C                 ADD             X8, X28, X9,LSL#47
LOAD:0000000000BED120                 STR             X19, [X23,#0x20]
LOAD:0000000000BED124                 MOV             X0, X23
LOAD:0000000000BED128                 STR             X8, [SP,#arg_18]
LOAD:0000000000BED12C                 ADD             X2, SP, #arg_18
LOAD:0000000000BED130                 BL              sub_BF3194
LOAD:0000000000BED134                 LDR             X19, [X23,#0x20]
LOAD:0000000000BED138                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BED13C                 STR             X8, [X0]
LOAD:0000000000BED140                 B               loc_BED0D0
LOAD:0000000000BED144 ; ---------------------------------------------------------------------------
LOAD:0000000000BED144
LOAD:0000000000BED144 loc_BED144                              ; CODE XREF: BC_GGET+320↑j
LOAD:0000000000BED144                 LDR             X9, [X22,#0x40]
LOAD:0000000000BED148                 AND             W10, W10, #0xFFFFFFFB
LOAD:0000000000BED14C                 STR             X1, [X22,#0x40]
LOAD:0000000000BED150                 STRB            W10, [X1,#8]
LOAD:0000000000BED154                 STR             X9, [X1,#0x18]
LOAD:0000000000BED158                 B               loc_BED0D0
LOAD:0000000000BED158 ; END OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BED15C
LOAD:0000000000BED15C ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED15C
LOAD:0000000000BED15C
LOAD:0000000000BED15C BC_TSETB
LOAD:0000000000BED15C
LOAD:0000000000BED15C arg_18          =  0x18
LOAD:0000000000BED15C
LOAD:0000000000BED15C ; FUNCTION CHUNK AT LOAD:0000000000BEE1FC SIZE 00000014 BYTES
LOAD:0000000000BED15C ; FUNCTION CHUNK AT LOAD:0000000000BEE218 SIZE 00000058 BYTES
LOAD:0000000000BED15C
LOAD:0000000000BED15C                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BED160                 AND             X28, X28, #0xFF
LOAD:0000000000BED164                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BED168                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BED16C                 CMN             X15, #0xC
LOAD:0000000000BED170                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BED174                 B.NE            loc_BEE1FC
LOAD:0000000000BED178                 LDR             X2, [X1,#0x10]
LOAD:0000000000BED17C                 LDR             W0, [X1,#0x30]
LOAD:0000000000BED180                 ADD             X2, X2, X28,LSL#3
LOAD:0000000000BED184                 CMP             W28, W0
LOAD:0000000000BED188                 B.CS            loc_BEE1FC
LOAD:0000000000BED18C                 LDR             X9, [X2]
LOAD:0000000000BED190                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BED194                 LDRB            W10, [X1,#8]
LOAD:0000000000BED198                 CMP             X9, X26
LOAD:0000000000BED19C                 B.EQ            loc_BED1C0
LOAD:0000000000BED1A0
LOAD:0000000000BED1A0 loc_BED1A0                              ; CODE XREF: BC_TSETB+68↓j
LOAD:0000000000BED1A0                                         ; BC_TSETB+70↓j
LOAD:0000000000BED1A0                 STR             X8, [X2]
LOAD:0000000000BED1A4                 TBNZ            W10, #2, loc_BED1D4
LOAD:0000000000BED1A8
LOAD:0000000000BED1A8 loc_BED1A8                              ; CODE XREF: BC_TSETB+8C↓j
LOAD:0000000000BED1A8                 LDR             W16, [X21],#4
LOAD:0000000000BED1AC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED1B0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED1B4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED1B8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED1BC                 BR              X8
LOAD:0000000000BED1C0 ; ---------------------------------------------------------------------------
LOAD:0000000000BED1C0
LOAD:0000000000BED1C0 loc_BED1C0                              ; CODE XREF: BC_TSETB+40↑j
LOAD:0000000000BED1C0                 LDR             X0, [X1,#0x20]
LOAD:0000000000BED1C4                 CBZ             X0, loc_BED1A0
LOAD:0000000000BED1C8                 LDRB            W9, [X0,#0xA]
LOAD:0000000000BED1CC                 TBNZ            W9, #1, loc_BED1A0
LOAD:0000000000BED1D0                 B               loc_BEE1FC
LOAD:0000000000BED1D4 ; ---------------------------------------------------------------------------
LOAD:0000000000BED1D4
LOAD:0000000000BED1D4 loc_BED1D4                              ; CODE XREF: BC_TSETB+48↑j
LOAD:0000000000BED1D4                 LDR             X9, [X22,#0x40]
LOAD:0000000000BED1D8                 AND             W10, W10, #0xFFFFFFFB
LOAD:0000000000BED1DC                 STR             X1, [X22,#0x40]
LOAD:0000000000BED1E0                 STRB            W10, [X1,#8]
LOAD:0000000000BED1E4                 STR             X9, [X1,#0x18]
LOAD:0000000000BED1E8                 B               loc_BED1A8
LOAD:0000000000BED1E8 ; End of function BC_TSETB
LOAD:0000000000BED1E8
LOAD:0000000000BED1EC
LOAD:0000000000BED1EC ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED1EC
LOAD:0000000000BED1EC
LOAD:0000000000BED1EC BC_TSETM
LOAD:0000000000BED1EC
LOAD:0000000000BED1EC arg_8           =  8
LOAD:0000000000BED1EC arg_20          =  0x20
LOAD:0000000000BED1EC
LOAD:0000000000BED1EC                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED1F0
LOAD:0000000000BED1F0 loc_BED1F0                              ; CODE XREF: BC_TSETM+78↓j
LOAD:0000000000BED1F0                 LDR             W17, [SP,#arg_20]
LOAD:0000000000BED1F4                 LDUR            X1, [X27,#-8]
LOAD:0000000000BED1F8                 LDR             X9, [X20,X28,LSL#3]
LOAD:0000000000BED1FC                 SUB             X17, X17, #8
LOAD:0000000000BED200                 CBZ             X17, loc_BED23C
LOAD:0000000000BED204                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BED208                 LDR             W0, [X1,#0x30]
LOAD:0000000000BED20C                 ADD             W2, W9, W17,LSR#3
LOAD:0000000000BED210                 LDR             X3, [X1,#0x10]
LOAD:0000000000BED214                 CMP             X2, X0
LOAD:0000000000BED218                 ADD             X17, X27, X17
LOAD:0000000000BED21C                 B.HI            loc_BED254
LOAD:0000000000BED220                 ADD             X9, X3, W9,UXTW#3
LOAD:0000000000BED224                 LDRB            W10, [X1,#8]
LOAD:0000000000BED228
LOAD:0000000000BED228 loc_BED228                              ; CODE XREF: BC_TSETM+48↓j
LOAD:0000000000BED228                 LDR             X8, [X27],#8
LOAD:0000000000BED22C                 STR             X8, [X9],#8
LOAD:0000000000BED230                 CMP             X27, X17
LOAD:0000000000BED234                 B.CC            loc_BED228
LOAD:0000000000BED238                 TBNZ            W10, #2, loc_BED268
LOAD:0000000000BED23C
LOAD:0000000000BED23C loc_BED23C                              ; CODE XREF: BC_TSETM+14↑j
LOAD:0000000000BED23C                                         ; BC_TSETM+90↓j
LOAD:0000000000BED23C                 LDR             W16, [X21],#4
LOAD:0000000000BED240                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED244                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED248                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED24C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED250                 BR              X8
LOAD:0000000000BED254 ; ---------------------------------------------------------------------------
LOAD:0000000000BED254
LOAD:0000000000BED254 loc_BED254                              ; CODE XREF: BC_TSETM+30↑j
LOAD:0000000000BED254                 STR             X19, [X23,#0x20]
LOAD:0000000000BED258                 MOV             X0, X23
LOAD:0000000000BED25C                 STR             X21, [SP,#arg_8]
LOAD:0000000000BED260                 BL              sub_BF2E00
LOAD:0000000000BED264                 B               loc_BED1F0
LOAD:0000000000BED268 ; ---------------------------------------------------------------------------
LOAD:0000000000BED268
LOAD:0000000000BED268 loc_BED268                              ; CODE XREF: BC_TSETM+4C↑j
LOAD:0000000000BED268                 LDR             X9, [X22,#0x40]
LOAD:0000000000BED26C                 AND             W10, W10, #0xFFFFFFFB
LOAD:0000000000BED270                 STR             X1, [X22,#0x40]
LOAD:0000000000BED274                 STRB            W10, [X1,#8]
LOAD:0000000000BED278                 STR             X9, [X1,#0x18]
LOAD:0000000000BED27C                 B               loc_BED23C
LOAD:0000000000BED27C ; End of function BC_TSETM
LOAD:0000000000BED27C
LOAD:0000000000BED280
LOAD:0000000000BED280 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED280
LOAD:0000000000BED280
LOAD:0000000000BED280 BC_TSETR
LOAD:0000000000BED280
LOAD:0000000000BED280 arg_8           =  8
LOAD:0000000000BED280
LOAD:0000000000BED280 ; FUNCTION CHUNK AT LOAD:0000000000BEE270 SIZE 00000018 BYTES
LOAD:0000000000BED280
LOAD:0000000000BED280                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BED284                 AND             X28, X28, #0xFF
LOAD:0000000000BED288                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BED28C                 LDR             X9, [X19,X28,LSL#3]
LOAD:0000000000BED290                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BED294                 LDR             X0, [X1,#0x10]
LOAD:0000000000BED298                 LDRB            W10, [X1,#8]
LOAD:0000000000BED29C                 LDR             W3, [X1,#0x30]
LOAD:0000000000BED2A0                 ADD             X0, X0, W9,UXTW#3
LOAD:0000000000BED2A4                 TBNZ            W10, #2, loc_BED2D0
LOAD:0000000000BED2A8
LOAD:0000000000BED2A8 loc_BED2A8                              ; CODE XREF: BC_TSETR+64↓j
LOAD:0000000000BED2A8                 CMP             W9, W3
LOAD:0000000000BED2AC                 B.CS            loc_BEE270
LOAD:0000000000BED2B0
LOAD:0000000000BED2B0 loc_BED2B0                              ; CODE XREF: BC_TSETR+1004↓j
LOAD:0000000000BED2B0                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BED2B4                 STR             X8, [X0]
LOAD:0000000000BED2B8                 LDR             W16, [X21],#4
LOAD:0000000000BED2BC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED2C0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED2C4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED2C8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED2CC                 BR              X8
LOAD:0000000000BED2D0 ; ---------------------------------------------------------------------------
LOAD:0000000000BED2D0
LOAD:0000000000BED2D0 loc_BED2D0                              ; CODE XREF: BC_TSETR+24↑j
LOAD:0000000000BED2D0                 LDR             X8, [X22,#0x40]
LOAD:0000000000BED2D4                 AND             W10, W10, #0xFFFFFFFB
LOAD:0000000000BED2D8                 STR             X1, [X22,#0x40]
LOAD:0000000000BED2DC                 STRB            W10, [X1,#8]
LOAD:0000000000BED2E0                 STR             X8, [X1,#0x18]
LOAD:0000000000BED2E4                 B               loc_BED2A8
LOAD:0000000000BED2E4 ; End of function BC_TSETR
LOAD:0000000000BED2E4
LOAD:0000000000BED2E8
LOAD:0000000000BED2E8 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED2E8
LOAD:0000000000BED2E8
LOAD:0000000000BED2E8 BC_CALLM
LOAD:0000000000BED2E8
LOAD:0000000000BED2E8 arg_20          =  0x20
LOAD:0000000000BED2E8
LOAD:0000000000BED2E8 ; FUNCTION CHUNK AT LOAD:0000000000BEE3E0 SIZE 00000044 BYTES
LOAD:0000000000BED2E8
LOAD:0000000000BED2E8                 LDR             W8, [SP,#arg_20]
LOAD:0000000000BED2EC                 UBFIZ           X28, X28, #3, #8
LOAD:0000000000BED2F0                 ADD             X28, X28, X8
LOAD:0000000000BED2F4                 B               BC_CALL_Z
LOAD:0000000000BED2F8 ; ---------------------------------------------------------------------------
LOAD:0000000000BED2F8
LOAD:0000000000BED2F8 BC_CALL
LOAD:0000000000BED2F8                 UBFIZ           X28, X28, #3, #8
LOAD:0000000000BED2FC
LOAD:0000000000BED2FC BC_CALL_Z                               ; CODE XREF: BC_CALLM+C↑j
LOAD:0000000000BED2FC                 MOV             X17, X19
LOAD:0000000000BED300                 ADD             X19, X19, X27,LSL#3
LOAD:0000000000BED304                 LDR             X2, [X19]
LOAD:0000000000BED308                 SUB             X28, X28, #8
LOAD:0000000000BED30C                 ADD             X19, X19, #0x10
LOAD:0000000000BED310                 ASR             X15, X2, #0x2F ; '/'
LOAD:0000000000BED314                 CMN             X15, #9
LOAD:0000000000BED318                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BED31C                 B.NE            loc_BEE3E0
LOAD:0000000000BED320                 STUR            X21, [X19,#-8]
LOAD:0000000000BED324                 LDR             X21, [X2,#0x20]
LOAD:0000000000BED328                 LDR             W16, [X21],#4
LOAD:0000000000BED32C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED330                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED334                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED338                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED33C                 BR              X8
LOAD:0000000000BED33C ; End of function BC_CALLM
LOAD:0000000000BED33C
LOAD:0000000000BED340
LOAD:0000000000BED340 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED340
LOAD:0000000000BED340
LOAD:0000000000BED340 BC_CALLMT
LOAD:0000000000BED340
LOAD:0000000000BED340 arg_8           =  8
LOAD:0000000000BED340 arg_20          =  0x20
LOAD:0000000000BED340
LOAD:0000000000BED340 ; FUNCTION CHUNK AT LOAD:0000000000BEE424 SIZE 0000002C BYTES
LOAD:0000000000BED340
LOAD:0000000000BED340                 LDR             W8, [SP,#arg_20]
LOAD:0000000000BED344                 ADD             X28, X8, X28,LSL#3
LOAD:0000000000BED348                 B               BC_CALLT1_Z
LOAD:0000000000BED34C ; ---------------------------------------------------------------------------
LOAD:0000000000BED34C
LOAD:0000000000BED34C BC_CALLT
LOAD:0000000000BED34C                 LSL             X28, X28, #3
LOAD:0000000000BED350
LOAD:0000000000BED350 BC_CALLT1_Z                             ; CODE XREF: BC_CALLMT+8↑j
LOAD:0000000000BED350                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED354                 LDR             X9, [X27]
LOAD:0000000000BED358                 SUB             X28, X28, #8
LOAD:0000000000BED35C                 ADD             X27, X27, #0x10
LOAD:0000000000BED360                 ASR             X15, X9, #0x2F ; '/'
LOAD:0000000000BED364                 CMN             X15, #9
LOAD:0000000000BED368                 AND             X2, X9, #0x7FFFFFFFFFFF
LOAD:0000000000BED36C                 B.NE            loc_BEE424
LOAD:0000000000BED370                 LDUR            X21, [X19,#-8]
LOAD:0000000000BED374
LOAD:0000000000BED374 BC_CALLT2_Z                             ; CODE XREF: BC_CALLMT+110C↓j
LOAD:0000000000BED374                 MOV             W17, #0
LOAD:0000000000BED378                 LDRB            W10, [X2,#0xA]
LOAD:0000000000BED37C                 TST             X21, #3
LOAD:0000000000BED380                 B.NE            loc_BED3E4
LOAD:0000000000BED384
LOAD:0000000000BED384 loc_BED384                              ; CODE XREF: BC_CALLMT+B0↓j
LOAD:0000000000BED384                                         ; BC_CALLMT+C4↓j
LOAD:0000000000BED384                 STUR            X9, [X19,#-0x10]
LOAD:0000000000BED388                 CBZ             X28, loc_BED3A4
LOAD:0000000000BED38C
LOAD:0000000000BED38C loc_BED38C                              ; CODE XREF: BC_CALLMT+60↓j
LOAD:0000000000BED38C                 LDR             X8, [X27,X17]
LOAD:0000000000BED390                 ADD             X9, X17, #8
LOAD:0000000000BED394                 CMP             X9, X28
LOAD:0000000000BED398                 STR             X8, [X19,X17]
LOAD:0000000000BED39C                 MOV             X17, X9
LOAD:0000000000BED3A0                 B.NE            loc_BED38C
LOAD:0000000000BED3A4
LOAD:0000000000BED3A4 loc_BED3A4                              ; CODE XREF: BC_CALLMT+48↑j
LOAD:0000000000BED3A4                 CMP             X10, #1
LOAD:0000000000BED3A8                 B.HI            loc_BED3C8
LOAD:0000000000BED3AC
LOAD:0000000000BED3AC loc_BED3AC                              ; CODE XREF: BC_CALLMT+A0↓j
LOAD:0000000000BED3AC                 LDR             X21, [X2,#0x20]
LOAD:0000000000BED3B0                 LDR             W16, [X21],#4
LOAD:0000000000BED3B4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED3B8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED3BC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED3C0                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED3C4                 BR              X8
LOAD:0000000000BED3C8 ; ---------------------------------------------------------------------------
LOAD:0000000000BED3C8
LOAD:0000000000BED3C8 loc_BED3C8                              ; CODE XREF: BC_CALLMT+68↑j
LOAD:0000000000BED3C8                 LDURB           W27, [X21,#-3]
LOAD:0000000000BED3CC                 SUB             X0, X19, X27,LSL#3
LOAD:0000000000BED3D0                 LDUR            X0, [X0,#-0x20]
LOAD:0000000000BED3D4                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BED3D8                 LDR             X0, [X0,#0x20]
LOAD:0000000000BED3DC                 LDUR            X20, [X0,#-0x48]
LOAD:0000000000BED3E0                 B               loc_BED3AC
LOAD:0000000000BED3E4 ; ---------------------------------------------------------------------------
LOAD:0000000000BED3E4
LOAD:0000000000BED3E4 loc_BED3E4                              ; CODE XREF: BC_CALLMT+40↑j
LOAD:0000000000BED3E4                 EOR             X21, X21, #3
LOAD:0000000000BED3E8                 TST             X21, #7
LOAD:0000000000BED3EC                 CSEL            X10, X17, X10, NE
LOAD:0000000000BED3F0                 B.NE            loc_BED384
LOAD:0000000000BED3F4                 SUB             X19, X19, X21
LOAD:0000000000BED3F8                 LDUR            X21, [X19,#-8]
LOAD:0000000000BED3FC                 TST             X21, #3
LOAD:0000000000BED400                 CSEL            X10, X17, X10, NE
LOAD:0000000000BED404                 B               loc_BED384
LOAD:0000000000BED404 ; End of function BC_CALLMT
LOAD:0000000000BED404
LOAD:0000000000BED408
LOAD:0000000000BED408 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED408
LOAD:0000000000BED408
LOAD:0000000000BED408 BC_ITERC
LOAD:0000000000BED408
LOAD:0000000000BED408 ; FUNCTION CHUNK AT LOAD:0000000000BEE3E0 SIZE 00000044 BYTES
LOAD:0000000000BED408
LOAD:0000000000BED408                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED40C                 LDUR            X2, [X27,#-0x18]
LOAD:0000000000BED410                 MOV             X17, X19
LOAD:0000000000BED414                 LDP             X0, X1, [X27,#-0x10]
LOAD:0000000000BED418                 ADD             X19, X27, #0x10
LOAD:0000000000BED41C                 MOV             W28, #0x10
LOAD:0000000000BED420                 STR             X2, [X27]
LOAD:0000000000BED424                 STP             X0, X1, [X27,#0x10]
LOAD:0000000000BED428                 ASR             X15, X2, #0x2F ; '/'
LOAD:0000000000BED42C                 CMN             X15, #9
LOAD:0000000000BED430                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BED434                 B.NE            loc_BEE3E0
LOAD:0000000000BED438                 STUR            X21, [X19,#-8]
LOAD:0000000000BED43C                 LDR             X21, [X2,#0x20]
LOAD:0000000000BED440                 LDR             W16, [X21],#4
LOAD:0000000000BED444                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED448                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED44C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED450                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED454                 BR              X8
LOAD:0000000000BED454 ; End of function BC_ITERC
LOAD:0000000000BED454
LOAD:0000000000BED458
LOAD:0000000000BED458 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED458
LOAD:0000000000BED458
LOAD:0000000000BED458 hotcheck
LOAD:0000000000BED458
LOAD:0000000000BED458 arg_8           =  8
LOAD:0000000000BED458
LOAD:0000000000BED458 ; FUNCTION CHUNK AT LOAD:0000000000BEF530 SIZE 00000034 BYTES
LOAD:0000000000BED458
LOAD:0000000000BED458                 LSR             X0, X21, #1
LOAD:0000000000BED45C                 AND             X0, X0, #0x7E ; '~'
LOAD:0000000000BED460                 ADD             X0, X0, #0xEF0
LOAD:0000000000BED464                 LDRH            W1, [X22,X0]
LOAD:0000000000BED468                 SUBS            X1, X1, #2
LOAD:0000000000BED46C                 STRH            W1, [X22,X0]
LOAD:0000000000BED470                 B.CC            loc_BEF530
LOAD:0000000000BED470 ; End of function hotcheck
LOAD:0000000000BED470
LOAD:0000000000BED474
LOAD:0000000000BED474 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED474
LOAD:0000000000BED474
LOAD:0000000000BED474 BC_ITERN                                ; DATA XREF: sub_BCD3A8+98↑o
LOAD:0000000000BED474                                         ; sub_BCD558+D4↑o ...
LOAD:0000000000BED474                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED478                 LDUR            X17, [X27,#-0x10]
LOAD:0000000000BED47C                 LDRH            W11, [X21,#2]
LOAD:0000000000BED480                 LDUR            W0, [X27,#-8]
LOAD:0000000000BED484                 ADD             X21, X21, #4
LOAD:0000000000BED488                 ADD             X11, X21, X11,LSL#2
LOAD:0000000000BED48C                 AND             X17, X17, #0x7FFFFFFFFFFF
LOAD:0000000000BED490                 SUB             X11, X11, #0x20,LSL#12 ; ' '
LOAD:0000000000BED494                 LDR             W9, [X17,#0x30]
LOAD:0000000000BED498                 LDR             X1, [X17,#0x10]
LOAD:0000000000BED49C
LOAD:0000000000BED49C loc_BED49C                              ; CODE XREF: BC_ITERN+40↓j
LOAD:0000000000BED49C                 SUBS            X28, X0, X9
LOAD:0000000000BED4A0                 ADD             X2, X1, X0,LSL#3
LOAD:0000000000BED4A4                 B.CS            loc_BED4E4
LOAD:0000000000BED4A8                 LDR             X8, [X2]
LOAD:0000000000BED4AC                 CMP             X8, X26
LOAD:0000000000BED4B0                 CINC            X0, X0, EQ
LOAD:0000000000BED4B4                 B.EQ            loc_BED49C
LOAD:0000000000BED4B8                 ADD             X0, X0, X24
LOAD:0000000000BED4BC                 STP             X0, X8, [X27]
LOAD:0000000000BED4C0                 ADD             X0, X0, #1
LOAD:0000000000BED4C4
LOAD:0000000000BED4C4 loc_BED4C4                              ; CODE XREF: BC_ITERN+A0↓j
LOAD:0000000000BED4C4                 STUR            W0, [X27,#-8]
LOAD:0000000000BED4C8                 MOV             X21, X11
LOAD:0000000000BED4CC
LOAD:0000000000BED4CC loc_BED4CC                              ; CODE XREF: BC_ITERN+84↓j
LOAD:0000000000BED4CC                 LDR             W16, [X21],#4
LOAD:0000000000BED4D0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED4D4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED4D8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED4DC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED4E0                 BR              X8
LOAD:0000000000BED4E4 ; ---------------------------------------------------------------------------
LOAD:0000000000BED4E4
LOAD:0000000000BED4E4 loc_BED4E4                              ; CODE XREF: BC_ITERN+30↑j
LOAD:0000000000BED4E4                 LDR             W10, [X17,#0x34]
LOAD:0000000000BED4E8                 LDR             X17, [X17,#0x28]
LOAD:0000000000BED4EC
LOAD:0000000000BED4EC loc_BED4EC                              ; CODE XREF: BC_ITERN+94↓j
LOAD:0000000000BED4EC                 ADD             X0, X28, X28,LSL#1
LOAD:0000000000BED4F0                 CMP             X28, X10
LOAD:0000000000BED4F4                 ADD             X2, X17, X0,LSL#3
LOAD:0000000000BED4F8                 B.HI            loc_BED4CC
LOAD:0000000000BED4FC                 LDP             X8, X0, [X2]
LOAD:0000000000BED500                 CMP             X8, X26
LOAD:0000000000BED504                 ADD             X28, X28, #1
LOAD:0000000000BED508                 B.EQ            loc_BED4EC
LOAD:0000000000BED50C                 STP             X0, X8, [X27]
LOAD:0000000000BED510                 ADD             X0, X28, X9
LOAD:0000000000BED514                 B               loc_BED4C4
LOAD:0000000000BED514 ; End of function BC_ITERN
LOAD:0000000000BED514
LOAD:0000000000BED518
LOAD:0000000000BED518 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED518
LOAD:0000000000BED518
LOAD:0000000000BED518 BC_VARG
LOAD:0000000000BED518
LOAD:0000000000BED518 arg_8           =  8
LOAD:0000000000BED518 arg_20          =  0x20
LOAD:0000000000BED518
LOAD:0000000000BED518                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BED51C                 AND             X28, X28, #0xFF
LOAD:0000000000BED520                 LDUR            X9, [X19,#-8]
LOAD:0000000000BED524                 ADD             X28, X19, X28,LSL#3
LOAD:0000000000BED528                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED52C                 ADD             X28, X28, #3
LOAD:0000000000BED530                 ADD             X10, X27, X17,LSL#3
LOAD:0000000000BED534                 SUB             X28, X28, X9
LOAD:0000000000BED538                 SUB             X11, X19, #0x10
LOAD:0000000000BED53C                 CBZ             X17, loc_BED574
LOAD:0000000000BED540                 SUB             X10, X10, #0x10
LOAD:0000000000BED544
LOAD:0000000000BED544 loc_BED544                              ; CODE XREF: BC_VARG+40↓j
LOAD:0000000000BED544                 CMP             X28, X11
LOAD:0000000000BED548                 LDR             X8, [X28],#8
LOAD:0000000000BED54C                 CSEL            X8, X8, X26, CC
LOAD:0000000000BED550                 CMP             X27, X10
LOAD:0000000000BED554                 STR             X8, [X27],#8
LOAD:0000000000BED558                 B.CC            loc_BED544
LOAD:0000000000BED55C
LOAD:0000000000BED55C loc_BED55C                              ; CODE XREF: BC_VARG+74↓j
LOAD:0000000000BED55C                                         ; BC_VARG+90↓j
LOAD:0000000000BED55C                 LDR             W16, [X21],#4
LOAD:0000000000BED560                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED564                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED568                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED56C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED570                 BR              X8
LOAD:0000000000BED574 ; ---------------------------------------------------------------------------
LOAD:0000000000BED574
LOAD:0000000000BED574 loc_BED574                              ; CODE XREF: BC_VARG+24↑j
LOAD:0000000000BED574                 LDR             X8, [X23,#0x30]
LOAD:0000000000BED578                 SUBS            X10, X11, X28
LOAD:0000000000BED57C                 CSEL            X17, XZR, X10, LE
LOAD:0000000000BED580                 ADD             X17, X17, #8
LOAD:0000000000BED584                 ADD             X9, X27, X10
LOAD:0000000000BED588                 STR             W17, [SP,#arg_20]
LOAD:0000000000BED58C                 B.LE            loc_BED55C
LOAD:0000000000BED590                 CMP             X9, X8
LOAD:0000000000BED594                 B.HI            loc_BED5AC
LOAD:0000000000BED598
LOAD:0000000000BED598 loc_BED598                              ; CODE XREF: BC_VARG+8C↓j
LOAD:0000000000BED598                                         ; BC_VARG+B8↓j
LOAD:0000000000BED598                 LDR             X8, [X28],#8
LOAD:0000000000BED59C                 STR             X8, [X27],#8
LOAD:0000000000BED5A0                 CMP             X28, X11
LOAD:0000000000BED5A4                 B.CC            loc_BED598
LOAD:0000000000BED5A8                 B               loc_BED55C
LOAD:0000000000BED5AC ; ---------------------------------------------------------------------------
LOAD:0000000000BED5AC
LOAD:0000000000BED5AC loc_BED5AC                              ; CODE XREF: BC_VARG+7C↑j
LOAD:0000000000BED5AC                 LSR             X1, X10, #3
LOAD:0000000000BED5B0                 STP             X19, X27, [X23,#0x20]
LOAD:0000000000BED5B4                 MOV             X0, X23
LOAD:0000000000BED5B8                 SUB             X28, X28, X19
LOAD:0000000000BED5BC                 STR             X21, [SP,#arg_8]
LOAD:0000000000BED5C0                 BL              sub_BCCCCC
LOAD:0000000000BED5C4                 LDP             X19, X27, [X23,#0x20]
LOAD:0000000000BED5C8                 ADD             X28, X19, X28
LOAD:0000000000BED5CC                 SUB             X11, X19, #0x10
LOAD:0000000000BED5D0                 B               loc_BED598
LOAD:0000000000BED5D0 ; End of function BC_VARG
LOAD:0000000000BED5D0
LOAD:0000000000BED5D4
LOAD:0000000000BED5D4 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED5D4
LOAD:0000000000BED5D4
LOAD:0000000000BED5D4 BC_ISNEXT
LOAD:0000000000BED5D4                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED5D8                 LDUR            X0, [X27,#-0x18]
LOAD:0000000000BED5DC                 ADD             X28, X21, X28,LSL#2
LOAD:0000000000BED5E0                 LDP             X2, X3, [X27,#-0x10]
LOAD:0000000000BED5E4                 SUB             X28, X28, #0x20,LSL#12 ; ' '
LOAD:0000000000BED5E8                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BED5EC                 CMN             X15, #9
LOAD:0000000000BED5F0                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BED5F4                 B.NE            loc_BED638
LOAD:0000000000BED5F8                 ASR             X8, X2, #0x2F ; '/'
LOAD:0000000000BED5FC                 LDRB            W9, [X0,#0xA]
LOAD:0000000000BED600                 CMN             X8, #0xC
LOAD:0000000000BED604                 CCMP            X3, X26, #0, EQ
LOAD:0000000000BED608                 CCMP            W9, #4, #0, EQ
LOAD:0000000000BED60C                 B.NE            loc_BED638
LOAD:0000000000BED610                 MOV             W8, #0
LOAD:0000000000BED618                 STUR            X8, [X27,#-8]
LOAD:0000000000BED61C
LOAD:0000000000BED61C loc_BED61C                              ; CODE XREF: BC_ISNEXT+80↓j
LOAD:0000000000BED61C                                         ; BC_ISNEXT+9C↓j
LOAD:0000000000BED61C                 MOV             X21, X28
LOAD:0000000000BED620                 LDR             W16, [X21],#4
LOAD:0000000000BED624                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED628                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED62C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED630                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED634                 BR              X8
LOAD:0000000000BED638 ; ---------------------------------------------------------------------------
LOAD:0000000000BED638
LOAD:0000000000BED638 loc_BED638                              ; CODE XREF: BC_ISNEXT+20↑j
LOAD:0000000000BED638                                         ; BC_ISNEXT+38↑j
LOAD:0000000000BED638                 LDRB            W10, [X28]
LOAD:0000000000BED63C                 MOV             W8, #0x68 ; 'h'
LOAD:0000000000BED640                 MOV             W9, #0x55 ; 'U'
LOAD:0000000000BED644                 STURB           W8, [X21,#-4]
LOAD:0000000000BED648                 CMP             W10, #0x56 ; 'V'
LOAD:0000000000BED64C                 B.NE            loc_BED658
LOAD:0000000000BED650                 STRB            W9, [X28]
LOAD:0000000000BED654                 B               loc_BED61C
LOAD:0000000000BED658 ; ---------------------------------------------------------------------------
LOAD:0000000000BED658
LOAD:0000000000BED658 loc_BED658                              ; CODE XREF: BC_ISNEXT+78↑j
LOAD:0000000000BED658                 LDR             X27, [X22,#0x440]
LOAD:0000000000BED65C                 LDRH            W10, [X28,#2]
LOAD:0000000000BED660                 LDR             X27, [X27,X10,LSL#3]
LOAD:0000000000BED664                 LDR             W10, [X27,#0x50]
LOAD:0000000000BED668                 BFXIL           W10, W9, #0, #8
LOAD:0000000000BED66C                 STR             W10, [X28]
LOAD:0000000000BED670                 B               loc_BED61C
LOAD:0000000000BED670 ; End of function BC_ISNEXT
LOAD:0000000000BED670
LOAD:0000000000BED674
LOAD:0000000000BED674 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED674
LOAD:0000000000BED674
LOAD:0000000000BED674 BC_RETM
LOAD:0000000000BED674
LOAD:0000000000BED674 arg_20          =  0x20
LOAD:0000000000BED674
LOAD:0000000000BED674 ; FUNCTION CHUNK AT LOAD:0000000000BED71C SIZE 00000014 BYTES
LOAD:0000000000BED674
LOAD:0000000000BED674                 LDR             W8, [SP,#arg_20]
LOAD:0000000000BED678                 LDUR            X21, [X19,#-8]
LOAD:0000000000BED67C                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED680                 ADD             X28, X8, X28,LSL#3
LOAD:0000000000BED684                 B               loc_BED694
LOAD:0000000000BED688 ; ---------------------------------------------------------------------------
LOAD:0000000000BED688
LOAD:0000000000BED688 BC_RET
LOAD:0000000000BED688                 LDUR            X21, [X19,#-8]
LOAD:0000000000BED68C                 LSL             X28, X28, #3
LOAD:0000000000BED690                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED694
LOAD:0000000000BED694 loc_BED694                              ; CODE XREF: BC_RETM+10↑j
LOAD:0000000000BED694                 STR             W28, [SP,#arg_20]
LOAD:0000000000BED698
LOAD:0000000000BED698 BC_RET_Z                                ; CODE XREF: BC_RETM+B8↓j
LOAD:0000000000BED698                 ANDS            X0, X21, #3
LOAD:0000000000BED69C                 EOR             X1, X21, #3
LOAD:0000000000BED6A0                 B.NE            loc_BED71C
LOAD:0000000000BED6A0 ; End of function BC_RETM
LOAD:0000000000BED6A0
LOAD:0000000000BED6A4 ; START OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BED6A4 ;   ADDITIONAL PARENT FUNCTION sub_BEDE9C
LOAD:0000000000BED6A4
LOAD:0000000000BED6A4 loc_BED6A4                              ; CODE XREF: sub_BEDE10-E8↓j
LOAD:0000000000BED6A4                                         ; sub_BEDE9C+98↓j ...
LOAD:0000000000BED6A4                 LDUR            W16, [X21,#-4]
LOAD:0000000000BED6A8                 SUBS            X9, X28, #8
LOAD:0000000000BED6AC                 SUB             X2, X19, #0x10
LOAD:0000000000BED6B0                 B.EQ            loc_BED6C8
LOAD:0000000000BED6B4
LOAD:0000000000BED6B4 loc_BED6B4                              ; CODE XREF: sub_BEDE10-74C↓j
LOAD:0000000000BED6B4                 LDR             X8, [X27],#8
LOAD:0000000000BED6B8                 ADD             X19, X19, #8
LOAD:0000000000BED6BC                 SUB             X9, X9, #8
LOAD:0000000000BED6C0                 STUR            X8, [X19,#-0x18]
LOAD:0000000000BED6C4                 CBNZ            X9, loc_BED6B4
LOAD:0000000000BED6C8
LOAD:0000000000BED6C8 loc_BED6C8                              ; CODE XREF: sub_BEDE10-760↑j
LOAD:0000000000BED6C8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED6CC                 SUB             X3, X2, X27,LSL#3
LOAD:0000000000BED6D0                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BED6D4                 LDUR            X0, [X3,#-0x10]
LOAD:0000000000BED6D8
LOAD:0000000000BED6D8 loc_BED6D8                              ; CODE XREF: sub_BEDE10-6FC↓j
LOAD:0000000000BED6D8                 CMP             X28, X17,LSL#3
LOAD:0000000000BED6DC                 B.CC            loc_BED708
LOAD:0000000000BED6E0                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BED6E4                 MOV             X19, X3
LOAD:0000000000BED6E8                 LDR             X1, [X0,#0x20]
LOAD:0000000000BED6EC                 LDUR            X20, [X1,#-0x48]
LOAD:0000000000BED6F0                 LDR             W16, [X21],#4
LOAD:0000000000BED6F4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED6F8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED6FC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED700                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED704                 BR              X8
LOAD:0000000000BED708 ; ---------------------------------------------------------------------------
LOAD:0000000000BED708
LOAD:0000000000BED708 loc_BED708                              ; CODE XREF: sub_BEDE10-734↑j
LOAD:0000000000BED708                 ADD             X19, X19, #8
LOAD:0000000000BED70C                 ADD             X28, X28, #8
LOAD:0000000000BED710                 STUR            X26, [X19,#-0x18]
LOAD:0000000000BED714                 B               loc_BED6D8
LOAD:0000000000BED714 ; END OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BED718 ; ---------------------------------------------------------------------------
LOAD:0000000000BED718 ; START OF FUNCTION CHUNK FOR BC_RET0
LOAD:0000000000BED718 ;   ADDITIONAL PARENT FUNCTION BC_RET1
LOAD:0000000000BED718
LOAD:0000000000BED718 loc_BED718                              ; CODE XREF: BC_RET0+14↓j
LOAD:0000000000BED718                                         ; BC_RET1+14↓j
LOAD:0000000000BED718                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED718 ; END OF FUNCTION CHUNK FOR BC_RET0
LOAD:0000000000BED71C ; START OF FUNCTION CHUNK FOR BC_RETM
LOAD:0000000000BED71C
LOAD:0000000000BED71C loc_BED71C                              ; CODE XREF: BC_RETM+2C↑j
LOAD:0000000000BED71C                 TST             X1, #7
LOAD:0000000000BED720                 B.NE            loc_BEDD2C
LOAD:0000000000BED724                 SUB             X19, X19, X1
LOAD:0000000000BED728                 LDUR            X21, [X19,#-8]
LOAD:0000000000BED72C                 B               BC_RET_Z
LOAD:0000000000BED72C ; END OF FUNCTION CHUNK FOR BC_RETM
LOAD:0000000000BED730
LOAD:0000000000BED730 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED730
LOAD:0000000000BED730
LOAD:0000000000BED730 ; __int64 __fastcall BC_RET0(__int64, __int64, __int64)
LOAD:0000000000BED730 BC_RET0
LOAD:0000000000BED730
LOAD:0000000000BED730 arg_20          =  0x20
LOAD:0000000000BED730
LOAD:0000000000BED730 ; FUNCTION CHUNK AT LOAD:0000000000BED718 SIZE 00000004 BYTES
LOAD:0000000000BED730
LOAD:0000000000BED730                 LDUR            X21, [X19,#-8]
LOAD:0000000000BED734                 LSL             X28, X28, #3
LOAD:0000000000BED738                 STR             W28, [SP,#arg_20]
LOAD:0000000000BED73C                 ANDS            X0, X21, #3
LOAD:0000000000BED740                 EOR             X1, X21, #3
LOAD:0000000000BED744                 B.NE            loc_BED718
LOAD:0000000000BED748                 LDUR            W16, [X21,#-4]
LOAD:0000000000BED74C                 SUB             X3, X19, #0x10
LOAD:0000000000BED750                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED754                 SUB             X19, X3, X27,LSL#3
LOAD:0000000000BED758                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BED75C                 LDUR            X0, [X19,#-0x10]
LOAD:0000000000BED760
LOAD:0000000000BED760 loc_BED760                              ; CODE XREF: BC_RET0+64↓j
LOAD:0000000000BED760                 CMP             X28, X17,LSL#3
LOAD:0000000000BED764                 B.CC            loc_BED78C
LOAD:0000000000BED768                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BED76C                 LDR             X1, [X0,#0x20]
LOAD:0000000000BED770                 LDUR            X20, [X1,#-0x48]
LOAD:0000000000BED774                 LDR             W16, [X21],#4
LOAD:0000000000BED778                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED77C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED780                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED784                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED788                 BR              X8
LOAD:0000000000BED78C ; ---------------------------------------------------------------------------
LOAD:0000000000BED78C
LOAD:0000000000BED78C loc_BED78C                              ; CODE XREF: BC_RET0+34↑j
LOAD:0000000000BED78C                 ADD             X28, X28, #8
LOAD:0000000000BED790                 STR             X26, [X3],#8
LOAD:0000000000BED794                 B               loc_BED760
LOAD:0000000000BED794 ; End of function BC_RET0
LOAD:0000000000BED794
LOAD:0000000000BED798
LOAD:0000000000BED798 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED798
LOAD:0000000000BED798
LOAD:0000000000BED798 BC_RET1
LOAD:0000000000BED798
LOAD:0000000000BED798 arg_20          =  0x20
LOAD:0000000000BED798
LOAD:0000000000BED798 ; FUNCTION CHUNK AT LOAD:0000000000BED718 SIZE 00000004 BYTES
LOAD:0000000000BED798
LOAD:0000000000BED798                 LDUR            X21, [X19,#-8]
LOAD:0000000000BED79C                 LSL             X28, X28, #3
LOAD:0000000000BED7A0                 STR             W28, [SP,#arg_20]
LOAD:0000000000BED7A4                 ANDS            X0, X21, #3
LOAD:0000000000BED7A8                 EOR             X1, X21, #3
LOAD:0000000000BED7AC                 B.NE            loc_BED718
LOAD:0000000000BED7B0                 LDUR            W16, [X21,#-4]
LOAD:0000000000BED7B4                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BED7B8                 SUB             X3, X19, #0x10
LOAD:0000000000BED7BC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED7C0                 SUB             X19, X3, X27,LSL#3
LOAD:0000000000BED7C4                 STR             X8, [X3],#8
LOAD:0000000000BED7C8                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BED7CC                 LDUR            X0, [X19,#-0x10]
LOAD:0000000000BED7D0
LOAD:0000000000BED7D0 loc_BED7D0                              ; CODE XREF: BC_RET1+6C↓j
LOAD:0000000000BED7D0                 CMP             X28, X17,LSL#3
LOAD:0000000000BED7D4                 B.CC            loc_BED7FC
LOAD:0000000000BED7D8                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BED7DC                 LDR             X1, [X0,#0x20]
LOAD:0000000000BED7E0                 LDUR            X20, [X1,#-0x48]
LOAD:0000000000BED7E4                 LDR             W16, [X21],#4
LOAD:0000000000BED7E8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED7EC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED7F0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED7F4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED7F8                 BR              X8
LOAD:0000000000BED7FC ; ---------------------------------------------------------------------------
LOAD:0000000000BED7FC
LOAD:0000000000BED7FC loc_BED7FC                              ; CODE XREF: BC_RET1+3C↑j
LOAD:0000000000BED7FC                 ADD             X28, X28, #8
LOAD:0000000000BED800                 STR             X26, [X3],#8
LOAD:0000000000BED804                 B               loc_BED7D0
LOAD:0000000000BED804 ; End of function BC_RET1
LOAD:0000000000BED804
LOAD:0000000000BED808
LOAD:0000000000BED808 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED808
LOAD:0000000000BED808
LOAD:0000000000BED808 ; __int64 __fastcall BC_FORI(long double, long double)
LOAD:0000000000BED808 BC_FORI                                 ; CODE XREF: BC_FORI+C74↓j
LOAD:0000000000BED808
LOAD:0000000000BED808 arg_8           =  8
LOAD:0000000000BED808
LOAD:0000000000BED808 ; FUNCTION CHUNK AT LOAD:0000000000BEDB14 SIZE 00000024 BYTES
LOAD:0000000000BED808 ; FUNCTION CHUNK AT LOAD:0000000000BEE450 SIZE 00000030 BYTES
LOAD:0000000000BED808
LOAD:0000000000BED808                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED80C                 LDP             X0, X1, [X27]
LOAD:0000000000BED810                 LDR             X2, [X27,#0x10]
LOAD:0000000000BED814                 ADD             X28, X21, X28,LSL#2
LOAD:0000000000BED818                 SUB             X28, X28, #0x20,LSL#12 ; ' '
LOAD:0000000000BED81C                 CMP             X25, X0,LSR#32
LOAD:0000000000BED820                 B.NE            loc_BED864
LOAD:0000000000BED824                 CMP             X25, X1,LSR#32
LOAD:0000000000BED828                 B.NE            loc_BEE450
LOAD:0000000000BED82C                 CMP             X25, X2,LSR#32
LOAD:0000000000BED830                 B.NE            loc_BEE450
LOAD:0000000000BED834                 TBNZ            W2, #0x1F, loc_BED85C
LOAD:0000000000BED838                 CMP             W0, W1
LOAD:0000000000BED83C
LOAD:0000000000BED83C loc_BED83C                              ; CODE XREF: BC_FORI+58↓j
LOAD:0000000000BED83C                 CSEL            X21, X28, X21, GT
LOAD:0000000000BED840                 STR             X0, [X27,#0x18]
LOAD:0000000000BED844
LOAD:0000000000BED844 loc_BED844                              ; CODE XREF: BC_FORI+84↓j
LOAD:0000000000BED844                 LDR             W16, [X21],#4
LOAD:0000000000BED848                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED84C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED850                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED854                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED858                 BR              X8
LOAD:0000000000BED85C ; ---------------------------------------------------------------------------
LOAD:0000000000BED85C
LOAD:0000000000BED85C loc_BED85C                              ; CODE XREF: BC_FORI+2C↑j
LOAD:0000000000BED85C                 CMP             W1, W0
LOAD:0000000000BED860                 B               loc_BED83C
LOAD:0000000000BED864 ; ---------------------------------------------------------------------------
LOAD:0000000000BED864
LOAD:0000000000BED864 loc_BED864                              ; CODE XREF: BC_FORI+18↑j
LOAD:0000000000BED864                 LDP             D0, D1, [X27]
LOAD:0000000000BED868                 B.CC            loc_BEE450
LOAD:0000000000BED86C                 CMP             X25, X1,LSR#32
LOAD:0000000000BED870                 B.LS            loc_BEE450
LOAD:0000000000BED874                 CMP             X25, X2,LSR#32
LOAD:0000000000BED878                 B.LS            loc_BEE450
LOAD:0000000000BED87C                 STR             D0, [X27,#0x18]
LOAD:0000000000BED880                 TBNZ            X2, #0x3F, loc_BED890 ; '?'
LOAD:0000000000BED884                 FCMP            D0, D1
LOAD:0000000000BED888
LOAD:0000000000BED888 loc_BED888                              ; CODE XREF: BC_FORI+8C↓j
LOAD:0000000000BED888                 CSEL            X21, X28, X21, HI
LOAD:0000000000BED88C                 B               loc_BED844
LOAD:0000000000BED890 ; ---------------------------------------------------------------------------
LOAD:0000000000BED890
LOAD:0000000000BED890 loc_BED890                              ; CODE XREF: BC_FORI+78↑j
LOAD:0000000000BED890                 FCMP            D1, D0
LOAD:0000000000BED894                 B               loc_BED888
LOAD:0000000000BED898 ; ---------------------------------------------------------------------------
LOAD:0000000000BED898
LOAD:0000000000BED898 BC_JFORL                                ; CODE XREF: BC_FORI+C70↓j
LOAD:0000000000BED898                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED89C                 LDP             X0, X1, [X27]
LOAD:0000000000BED8A0                 LDR             X2, [X27,#0x10]
LOAD:0000000000BED8A4                 ADD             X28, X21, X28,LSL#2
LOAD:0000000000BED8A8                 SUB             X28, X28, #0x20,LSL#12 ; ' '
LOAD:0000000000BED8AC                 CMP             X25, X0,LSR#32
LOAD:0000000000BED8B0                 B.NE            loc_BED8FC
LOAD:0000000000BED8B4                 CMP             X25, X1,LSR#32
LOAD:0000000000BED8B8                 B.NE            loc_BEE450
LOAD:0000000000BED8BC                 CMP             X25, X2,LSR#32
LOAD:0000000000BED8C0                 B.NE            loc_BEE450
LOAD:0000000000BED8C4                 TBNZ            W2, #0x1F, loc_BED8F4
LOAD:0000000000BED8C8                 CMP             W0, W1
LOAD:0000000000BED8CC
LOAD:0000000000BED8CC loc_BED8CC                              ; CODE XREF: BC_FORI+F0↓j
LOAD:0000000000BED8CC                 MOV             X21, X28
LOAD:0000000000BED8D0                 LDURH           W28, [X28,#-2]
LOAD:0000000000BED8D4                 STR             X0, [X27,#0x18]
LOAD:0000000000BED8D8                 B.LE            BC_JLOOP
LOAD:0000000000BED8DC
LOAD:0000000000BED8DC loc_BED8DC                              ; CODE XREF: BC_FORI+120↓j
LOAD:0000000000BED8DC                 LDR             W16, [X21],#4
LOAD:0000000000BED8E0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED8E4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED8E8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED8EC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED8F0                 BR              X8
LOAD:0000000000BED8F4 ; ---------------------------------------------------------------------------
LOAD:0000000000BED8F4
LOAD:0000000000BED8F4 loc_BED8F4                              ; CODE XREF: BC_FORI+BC↑j
LOAD:0000000000BED8F4                 CMP             W1, W0
LOAD:0000000000BED8F8                 B               loc_BED8CC
LOAD:0000000000BED8FC ; ---------------------------------------------------------------------------
LOAD:0000000000BED8FC
LOAD:0000000000BED8FC loc_BED8FC                              ; CODE XREF: BC_FORI+A8↑j
LOAD:0000000000BED8FC                 LDP             D0, D1, [X27]
LOAD:0000000000BED900                 B.CC            loc_BEE450
LOAD:0000000000BED904                 CMP             X25, X1,LSR#32
LOAD:0000000000BED908                 B.LS            loc_BEE450
LOAD:0000000000BED90C                 CMP             X25, X2,LSR#32
LOAD:0000000000BED910                 B.LS            loc_BEE450
LOAD:0000000000BED914                 STR             D0, [X27,#0x18]
LOAD:0000000000BED918                 TBNZ            X2, #0x3F, loc_BED92C ; '?'
LOAD:0000000000BED91C                 FCMP            D0, D1
LOAD:0000000000BED920
LOAD:0000000000BED920 loc_BED920                              ; CODE XREF: BC_FORI+128↓j
LOAD:0000000000BED920                 LDURH           W28, [X28,#-2]
LOAD:0000000000BED924                 B.LS            BC_JLOOP
LOAD:0000000000BED928                 B               loc_BED8DC
LOAD:0000000000BED92C ; ---------------------------------------------------------------------------
LOAD:0000000000BED92C
LOAD:0000000000BED92C loc_BED92C                              ; CODE XREF: BC_FORI+110↑j
LOAD:0000000000BED92C                 FCMP            D1, D0
LOAD:0000000000BED930                 B               loc_BED920
LOAD:0000000000BED930 ; End of function BC_FORI
LOAD:0000000000BED930
LOAD:0000000000BED934
LOAD:0000000000BED934 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED934
LOAD:0000000000BED934
LOAD:0000000000BED934 BC_FORL
LOAD:0000000000BED934
LOAD:0000000000BED934 ; FUNCTION CHUNK AT LOAD:0000000000BEE450 SIZE 00000030 BYTES
LOAD:0000000000BED934 ; FUNCTION CHUNK AT LOAD:0000000000BEF530 SIZE 00000034 BYTES
LOAD:0000000000BED934
LOAD:0000000000BED934                 LSR             X0, X21, #1
LOAD:0000000000BED938                 AND             X0, X0, #0x7E ; '~'
LOAD:0000000000BED93C                 ADD             X0, X0, #0xEF0
LOAD:0000000000BED940                 LDRH            W1, [X22,X0]
LOAD:0000000000BED944                 SUBS            X1, X1, #2
LOAD:0000000000BED948                 STRH            W1, [X22,X0]
LOAD:0000000000BED94C                 B.CC            loc_BEF530
LOAD:0000000000BED950
LOAD:0000000000BED950 BC_IFORL2
LOAD:0000000000BED950                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED954                 LDP             X0, X1, [X27]
LOAD:0000000000BED958                 LDR             X2, [X27,#0x10]
LOAD:0000000000BED95C                 ADD             X28, X21, X28,LSL#2
LOAD:0000000000BED960                 SUB             X28, X28, #0x20,LSL#12 ; ' '
LOAD:0000000000BED964                 CMP             X25, X0,LSR#32
LOAD:0000000000BED968                 B.NE            loc_BED9AC
LOAD:0000000000BED96C                 ADDS            W0, W0, W2
LOAD:0000000000BED970                 B.VS            loc_BED98C
LOAD:0000000000BED974                 ADD             X8, X0, X24
LOAD:0000000000BED978                 TBNZ            W2, #0x1F, loc_BED9A4
LOAD:0000000000BED97C                 CMP             W0, W1
LOAD:0000000000BED980
LOAD:0000000000BED980 loc_BED980                              ; CODE XREF: BC_FORL+74↓j
LOAD:0000000000BED980                 CSEL            X21, X28, X21, LE
LOAD:0000000000BED984                 STR             X8, [X27]
LOAD:0000000000BED988                 STR             X8, [X27,#0x18]
LOAD:0000000000BED98C
LOAD:0000000000BED98C loc_BED98C                              ; CODE XREF: BC_FORL+3C↑j
LOAD:0000000000BED98C                                         ; BC_FORL+9C↓j
LOAD:0000000000BED98C                 LDR             W16, [X21],#4
LOAD:0000000000BED990                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BED994                 UBFX            X27, X16, #8, #8
LOAD:0000000000BED998                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BED99C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BED9A0                 BR              X8
LOAD:0000000000BED9A4 ; ---------------------------------------------------------------------------
LOAD:0000000000BED9A4
LOAD:0000000000BED9A4 loc_BED9A4                              ; CODE XREF: BC_FORL+44↑j
LOAD:0000000000BED9A4                 CMP             W1, W0
LOAD:0000000000BED9A8                 B               loc_BED980
LOAD:0000000000BED9AC ; ---------------------------------------------------------------------------
LOAD:0000000000BED9AC
LOAD:0000000000BED9AC loc_BED9AC                              ; CODE XREF: BC_FORL+34↑j
LOAD:0000000000BED9AC                 LDP             D0, D1, [X27]
LOAD:0000000000BED9B0                 B.CC            loc_BEE450
LOAD:0000000000BED9B4                 LDR             D2, [X27,#0x10]
LOAD:0000000000BED9B8                 FADD            D0, D0, D2
LOAD:0000000000BED9BC                 TBNZ            X2, #0x3F, loc_BED9D4 ; '?'
LOAD:0000000000BED9C0                 FCMP            D0, D1
LOAD:0000000000BED9C4
LOAD:0000000000BED9C4 loc_BED9C4                              ; CODE XREF: BC_FORL+A4↓j
LOAD:0000000000BED9C4                 STR             D0, [X27]
LOAD:0000000000BED9C8                 STR             D0, [X27,#0x18]
LOAD:0000000000BED9CC                 CSEL            X21, X28, X21, LS
LOAD:0000000000BED9D0                 B               loc_BED98C
LOAD:0000000000BED9D4 ; ---------------------------------------------------------------------------
LOAD:0000000000BED9D4
LOAD:0000000000BED9D4 loc_BED9D4                              ; CODE XREF: BC_FORL+88↑j
LOAD:0000000000BED9D4                 FCMP            D1, D0
LOAD:0000000000BED9D8                 B               loc_BED9C4
LOAD:0000000000BED9D8 ; End of function BC_FORL
LOAD:0000000000BED9D8
LOAD:0000000000BED9DC
LOAD:0000000000BED9DC ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BED9DC
LOAD:0000000000BED9DC
LOAD:0000000000BED9DC BC_IFORL
LOAD:0000000000BED9DC
LOAD:0000000000BED9DC ; FUNCTION CHUNK AT LOAD:0000000000BEDB14 SIZE 00000024 BYTES
LOAD:0000000000BED9DC ; FUNCTION CHUNK AT LOAD:0000000000BEE450 SIZE 00000030 BYTES
LOAD:0000000000BED9DC
LOAD:0000000000BED9DC                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BED9E0                 LDP             X0, X1, [X27]
LOAD:0000000000BED9E4                 LDR             X2, [X27,#0x10]
LOAD:0000000000BED9E8                 CMP             X25, X0,LSR#32
LOAD:0000000000BED9EC                 B.NE            loc_BEDA30
LOAD:0000000000BED9F0                 ADDS            W0, W0, W2
LOAD:0000000000BED9F4                 B.VS            loc_BEDA10
LOAD:0000000000BED9F8                 ADD             X8, X0, X24
LOAD:0000000000BED9FC                 TBNZ            W2, #0x1F, loc_BEDA28
LOAD:0000000000BEDA00                 CMP             W0, W1
LOAD:0000000000BEDA04
LOAD:0000000000BEDA04 loc_BEDA04                              ; CODE XREF: BC_IFORL+50↓j
LOAD:0000000000BEDA04                 STR             X8, [X27]
LOAD:0000000000BEDA08                 STR             X8, [X27,#0x18]
LOAD:0000000000BEDA0C                 B.LE            BC_JLOOP
LOAD:0000000000BEDA10
LOAD:0000000000BEDA10 loc_BEDA10                              ; CODE XREF: BC_IFORL+18↑j
LOAD:0000000000BEDA10                                         ; BC_IFORL+78↓j
LOAD:0000000000BEDA10                 LDR             W16, [X21],#4
LOAD:0000000000BEDA14                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEDA18                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEDA1C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEDA20                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEDA24                 BR              X8
LOAD:0000000000BEDA28 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDA28
LOAD:0000000000BEDA28 loc_BEDA28                              ; CODE XREF: BC_IFORL+20↑j
LOAD:0000000000BEDA28                 CMP             W1, W0
LOAD:0000000000BEDA2C                 B               loc_BEDA04
LOAD:0000000000BEDA30 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDA30
LOAD:0000000000BEDA30 loc_BEDA30                              ; CODE XREF: BC_IFORL+10↑j
LOAD:0000000000BEDA30                 LDP             D0, D1, [X27]
LOAD:0000000000BEDA34                 B.CC            loc_BEE450
LOAD:0000000000BEDA38                 LDR             D2, [X27,#0x10]
LOAD:0000000000BEDA3C                 FADD            D0, D0, D2
LOAD:0000000000BEDA40                 TBNZ            X2, #0x3F, loc_BEDA58 ; '?'
LOAD:0000000000BEDA44                 FCMP            D0, D1
LOAD:0000000000BEDA48
LOAD:0000000000BEDA48 loc_BEDA48                              ; CODE XREF: BC_IFORL+80↓j
LOAD:0000000000BEDA48                 STR             D0, [X27]
LOAD:0000000000BEDA4C                 STR             D0, [X27,#0x18]
LOAD:0000000000BEDA50                 B.LS            BC_JLOOP
LOAD:0000000000BEDA54                 B               loc_BEDA10
LOAD:0000000000BEDA58 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDA58
LOAD:0000000000BEDA58 loc_BEDA58                              ; CODE XREF: BC_IFORL+64↑j
LOAD:0000000000BEDA58                 FCMP            D1, D0
LOAD:0000000000BEDA5C                 B               loc_BEDA48
LOAD:0000000000BEDA5C ; End of function BC_IFORL
LOAD:0000000000BEDA5C
LOAD:0000000000BEDA60
LOAD:0000000000BEDA60 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDA60
LOAD:0000000000BEDA60
LOAD:0000000000BEDA60 BC_ITERL
LOAD:0000000000BEDA60
LOAD:0000000000BEDA60 ; FUNCTION CHUNK AT LOAD:0000000000BEF530 SIZE 00000034 BYTES
LOAD:0000000000BEDA60
LOAD:0000000000BEDA60                 LSR             X0, X21, #1
LOAD:0000000000BEDA64                 AND             X0, X0, #0x7E ; '~'
LOAD:0000000000BEDA68                 ADD             X0, X0, #0xEF0
LOAD:0000000000BEDA6C                 LDRH            W1, [X22,X0]
LOAD:0000000000BEDA70                 SUBS            X1, X1, #2
LOAD:0000000000BEDA74                 STRH            W1, [X22,X0]
LOAD:0000000000BEDA78                 B.CC            loc_BEF530
LOAD:0000000000BEDA7C                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEDA80                 ADD             X9, X19, X27,LSL#3
LOAD:0000000000BEDA84                 CMP             X0, X26
LOAD:0000000000BEDA88                 B.EQ            loc_BEDA98
LOAD:0000000000BEDA8C                 ADD             X8, X21, X28,LSL#2
LOAD:0000000000BEDA90                 SUB             X21, X8, #0x20,LSL#12 ; ' '
LOAD:0000000000BEDA94                 STUR            X0, [X9,#-8]
LOAD:0000000000BEDA98
LOAD:0000000000BEDA98 loc_BEDA98                              ; CODE XREF: BC_ITERL+28↑j
LOAD:0000000000BEDA98                 LDR             W16, [X21],#4
LOAD:0000000000BEDA9C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEDAA0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEDAA4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEDAA8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEDAAC                 BR              X8
LOAD:0000000000BEDAAC ; End of function BC_ITERL
LOAD:0000000000BEDAAC
LOAD:0000000000BEDAB0
LOAD:0000000000BEDAB0 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDAB0
LOAD:0000000000BEDAB0
LOAD:0000000000BEDAB0 BC_IITERL
LOAD:0000000000BEDAB0
LOAD:0000000000BEDAB0 ; FUNCTION CHUNK AT LOAD:0000000000BEDB14 SIZE 00000024 BYTES
LOAD:0000000000BEDAB0
LOAD:0000000000BEDAB0                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEDAB4                 ADD             X9, X19, X27,LSL#3
LOAD:0000000000BEDAB8                 CMP             X0, X26
LOAD:0000000000BEDABC                 B.EQ            loc_BEDAC8
LOAD:0000000000BEDAC0                 STUR            X0, [X9,#-8]
LOAD:0000000000BEDAC4                 B               BC_JLOOP
LOAD:0000000000BEDAC8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDAC8
LOAD:0000000000BEDAC8 loc_BEDAC8                              ; CODE XREF: BC_IITERL+C↑j
LOAD:0000000000BEDAC8                 LDR             W16, [X21],#4
LOAD:0000000000BEDACC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEDAD0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEDAD4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEDAD8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEDADC                 BR              X8
LOAD:0000000000BEDADC ; End of function BC_IITERL
LOAD:0000000000BEDADC
LOAD:0000000000BEDAE0
LOAD:0000000000BEDAE0 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDAE0
LOAD:0000000000BEDAE0
LOAD:0000000000BEDAE0 BC_LOOP
LOAD:0000000000BEDAE0
LOAD:0000000000BEDAE0 ; FUNCTION CHUNK AT LOAD:0000000000BEF530 SIZE 00000034 BYTES
LOAD:0000000000BEDAE0
LOAD:0000000000BEDAE0                 LSR             X0, X21, #1
LOAD:0000000000BEDAE4                 AND             X0, X0, #0x7E ; '~'
LOAD:0000000000BEDAE8                 ADD             X0, X0, #0xEF0
LOAD:0000000000BEDAEC                 LDRH            W1, [X22,X0]
LOAD:0000000000BEDAF0                 SUBS            X1, X1, #2
LOAD:0000000000BEDAF4                 STRH            W1, [X22,X0]
LOAD:0000000000BEDAF8                 B.CC            loc_BEF530
LOAD:0000000000BEDAFC                 LDR             W16, [X21],#4
LOAD:0000000000BEDB00                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEDB04                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEDB08                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEDB0C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEDB10                 BR              X8
LOAD:0000000000BEDB10 ; End of function BC_LOOP
LOAD:0000000000BEDB10
LOAD:0000000000BEDB14 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDB14 ; START OF FUNCTION CHUNK FOR BC_FORI
LOAD:0000000000BEDB14 ;   ADDITIONAL PARENT FUNCTION BC_IFORL
LOAD:0000000000BEDB14 ;   ADDITIONAL PARENT FUNCTION BC_IITERL
LOAD:0000000000BEDB14 ;   ADDITIONAL PARENT FUNCTION BC_IFUNCF
LOAD:0000000000BEDB14 ;   ADDITIONAL PARENT FUNCTION sub_BEF5A8
LOAD:0000000000BEDB14
LOAD:0000000000BEDB14 BC_JLOOP                                ; CODE XREF: BC_FORI+D0↑j
LOAD:0000000000BEDB14                                         ; BC_FORI+11C↑j ...
LOAD:0000000000BEDB14                 LDR             X0, [X22,#0x440]
LOAD:0000000000BEDB18                 MOV             W1, #0
LOAD:0000000000BEDB1C                 LDR             X28, [X0,X28,LSL#3]
LOAD:0000000000BEDB20                 STR             W1, [X22,#0xB8]
LOAD:0000000000BEDB24                 LDR             X27, [X28,#0x58]
LOAD:0000000000BEDB28                 STR             X19, [X22,#0x178]
LOAD:0000000000BEDB2C                 STR             X23, [X22,#0xE0]
LOAD:0000000000BEDB30                 SUB             SP, SP, #0x10
LOAD:0000000000BEDB34                 BR              X27
LOAD:0000000000BEDB34 ; END OF FUNCTION CHUNK FOR BC_FORI
LOAD:0000000000BEDB38
LOAD:0000000000BEDB38 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDB38
LOAD:0000000000BEDB38
LOAD:0000000000BEDB38 BC_JMP
LOAD:0000000000BEDB38                 ADD             X28, X21, X28,LSL#2
LOAD:0000000000BEDB3C                 SUB             X21, X28, #0x20,LSL#12 ; ' '
LOAD:0000000000BEDB40                 LDR             W16, [X21],#4
LOAD:0000000000BEDB44                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEDB48                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEDB4C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEDB50                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEDB54                 BR              X8
LOAD:0000000000BEDB54 ; End of function BC_JMP
LOAD:0000000000BEDB54
LOAD:0000000000BEDB58
LOAD:0000000000BEDB58 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDB58
LOAD:0000000000BEDB58
LOAD:0000000000BEDB58 ; __int64 BC_FUNCF()
LOAD:0000000000BEDB58 BC_FUNCF
LOAD:0000000000BEDB58
LOAD:0000000000BEDB58 arg_8           =  8
LOAD:0000000000BEDB58
LOAD:0000000000BEDB58 ; FUNCTION CHUNK AT LOAD:0000000000BEDE50 SIZE 0000004C BYTES
LOAD:0000000000BEDB58
LOAD:0000000000BEDB58                 LSR             X0, X21, #1
LOAD:0000000000BEDB5C                 AND             X0, X0, #0x7E ; '~'
LOAD:0000000000BEDB60                 ADD             X0, X0, #0xEF0
LOAD:0000000000BEDB64                 LDRH            W1, [X22,X0]
LOAD:0000000000BEDB68                 SUBS            X1, X1, #1
LOAD:0000000000BEDB6C                 STRH            W1, [X22,X0]
LOAD:0000000000BEDB70                 B.CC            loc_BEF56C
LOAD:0000000000BEDB74                 LDR             X0, [X23,#0x30]
LOAD:0000000000BEDB78                 LDURB           W9, [X21,#-0x62]
LOAD:0000000000BEDB7C                 LDUR            X20, [X21,#-0x4C]
LOAD:0000000000BEDB80                 CMP             X27, X0
LOAD:0000000000BEDB84                 B.HI            loc_BEDE50
LOAD:0000000000BEDB88
LOAD:0000000000BEDB88 loc_BEDB88                              ; CODE XREF: BC_FUNCF+58↓j
LOAD:0000000000BEDB88                 CMP             X28, X9,LSL#3
LOAD:0000000000BEDB8C                 B.CC            loc_BEDBA8
LOAD:0000000000BEDB90                 LDR             W16, [X21],#4
LOAD:0000000000BEDB94                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEDB98                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEDB9C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEDBA0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEDBA4                 BR              X8
LOAD:0000000000BEDBA8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDBA8
LOAD:0000000000BEDBA8 loc_BEDBA8                              ; CODE XREF: BC_FUNCF+34↑j
LOAD:0000000000BEDBA8                 STR             X26, [X19,X28]
LOAD:0000000000BEDBAC                 ADD             X28, X28, #8
LOAD:0000000000BEDBB0                 B               loc_BEDB88
LOAD:0000000000BEDBB0 ; End of function BC_FUNCF
LOAD:0000000000BEDBB0
LOAD:0000000000BEDBB4
LOAD:0000000000BEDBB4 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDBB4
LOAD:0000000000BEDBB4
LOAD:0000000000BEDBB4 BC_IFUNCF
LOAD:0000000000BEDBB4
LOAD:0000000000BEDBB4 ; FUNCTION CHUNK AT LOAD:0000000000BEDB14 SIZE 00000024 BYTES
LOAD:0000000000BEDBB4 ; FUNCTION CHUNK AT LOAD:0000000000BEDE50 SIZE 0000004C BYTES
LOAD:0000000000BEDBB4
LOAD:0000000000BEDBB4                 LDR             X0, [X23,#0x30]
LOAD:0000000000BEDBB8                 LDURB           W9, [X21,#-0x62]
LOAD:0000000000BEDBBC                 LDUR            X20, [X21,#-0x4C]
LOAD:0000000000BEDBC0                 CMP             X27, X0
LOAD:0000000000BEDBC4                 B.HI            loc_BEDE50
LOAD:0000000000BEDBC8
LOAD:0000000000BEDBC8 loc_BEDBC8                              ; CODE XREF: BC_IFUNCF+2C↓j
LOAD:0000000000BEDBC8                 CMP             X28, X9,LSL#3
LOAD:0000000000BEDBCC                 B.CC            loc_BEDBD8
LOAD:0000000000BEDBD0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEDBD4                 B               BC_JLOOP
LOAD:0000000000BEDBD8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDBD8
LOAD:0000000000BEDBD8 loc_BEDBD8                              ; CODE XREF: BC_IFUNCF+18↑j
LOAD:0000000000BEDBD8                 STR             X26, [X19,X28]
LOAD:0000000000BEDBDC                 ADD             X28, X28, #8
LOAD:0000000000BEDBE0                 B               loc_BEDBC8
LOAD:0000000000BEDBE0 ; End of function BC_IFUNCF
LOAD:0000000000BEDBE0
LOAD:0000000000BEDBE4
LOAD:0000000000BEDBE4 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDBE4
LOAD:0000000000BEDBE4
LOAD:0000000000BEDBE4 BC_JFUNCF
LOAD:0000000000BEDBE4
LOAD:0000000000BEDBE4 ; FUNCTION CHUNK AT LOAD:0000000000BEDE50 SIZE 0000004C BYTES
LOAD:0000000000BEDBE4
LOAD:0000000000BEDBE4                 LDR             X0, [X23,#0x30]
LOAD:0000000000BEDBE8                 MOV             X8, #0xFFFFFFFFFFFFFFF7
LOAD:0000000000BEDBEC                 ADD             X10, X19, X28
LOAD:0000000000BEDBF0                 ADD             X2, X2, X8,LSL#47
LOAD:0000000000BEDBF4                 ADD             X27, X27, X28
LOAD:0000000000BEDBF8                 ADD             X8, X28, #0x13
LOAD:0000000000BEDBFC                 STR             X2, [X10],#8
LOAD:0000000000BEDC00                 LDUR            X20, [X21,#-0x4C]
LOAD:0000000000BEDC04                 CMP             X27, X0
LOAD:0000000000BEDC08                 STR             X8, [X10],#8
LOAD:0000000000BEDC0C                 B.CS            loc_BEDE50
LOAD:0000000000BEDC10                 SUB             X28, X10, #0x10
LOAD:0000000000BEDC14                 LDURB           W9, [X21,#-0x62]
LOAD:0000000000BEDC18                 MOV             X27, X19
LOAD:0000000000BEDC1C                 MOV             X19, X10
LOAD:0000000000BEDC20                 CBZ             X9, loc_BEDC40
LOAD:0000000000BEDC24
LOAD:0000000000BEDC24 loc_BEDC24                              ; CODE XREF: BC_JFUNCF+58↓j
LOAD:0000000000BEDC24                 CMP             X27, X28
LOAD:0000000000BEDC28                 B.CS            loc_BEDC58
LOAD:0000000000BEDC2C                 LDR             X8, [X27]
LOAD:0000000000BEDC30                 SUB             X9, X9, #1
LOAD:0000000000BEDC34                 STR             X26, [X27],#8
LOAD:0000000000BEDC38                 STR             X8, [X10],#8
LOAD:0000000000BEDC3C                 CBNZ            X9, loc_BEDC24
LOAD:0000000000BEDC40
LOAD:0000000000BEDC40 loc_BEDC40                              ; CODE XREF: BC_JFUNCF+3C↑j
LOAD:0000000000BEDC40                                         ; BC_JFUNCF+7C↓j
LOAD:0000000000BEDC40                 LDR             W16, [X21],#4
LOAD:0000000000BEDC44                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEDC48                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEDC4C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEDC50                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEDC54                 BR              X8
LOAD:0000000000BEDC58 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDC58
LOAD:0000000000BEDC58 loc_BEDC58                              ; CODE XREF: BC_JFUNCF+44↑j
LOAD:0000000000BEDC58                                         ; BC_JFUNCF+80↓j
LOAD:0000000000BEDC58                 SUB             X9, X9, #1
LOAD:0000000000BEDC5C                 STR             X26, [X10],#8
LOAD:0000000000BEDC60                 CBZ             X9, loc_BEDC40
LOAD:0000000000BEDC64                 B               loc_BEDC58
LOAD:0000000000BEDC64 ; End of function BC_JFUNCF
LOAD:0000000000BEDC64
LOAD:0000000000BEDC68
LOAD:0000000000BEDC68 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDC68
LOAD:0000000000BEDC68
LOAD:0000000000BEDC68 ; void __fastcall BC_FUNCC(__int64, __int64, __int64)
LOAD:0000000000BEDC68 BC_FUNCC
LOAD:0000000000BEDC68
LOAD:0000000000BEDC68 ; FUNCTION CHUNK AT LOAD:0000000000BEDE48 SIZE 00000008 BYTES
LOAD:0000000000BEDC68
LOAD:0000000000BEDC68                 BRK             #0
LOAD:0000000000BEDC6C                 LDR             X3, [X2,#0x28]
LOAD:0000000000BEDC70                 ADD             X1, X27, X28
LOAD:0000000000BEDC74                 LDR             X0, [X23,#0x30]
LOAD:0000000000BEDC78                 ADD             X28, X19, X28
LOAD:0000000000BEDC7C                 CMP             X1, X0
LOAD:0000000000BEDC80                 STP             X19, X28, [X23,#0x20]
LOAD:0000000000BEDC84                 MOV             W8, #0xFFFFFFFE
LOAD:0000000000BEDC88                 MOV             X0, X23
LOAD:0000000000BEDC8C                 B.HI            loc_BEDE48
LOAD:0000000000BEDC90                 STR             W8, [X22,#0xB8]
LOAD:0000000000BEDC94                 BLR             X3
LOAD:0000000000BEDC98                 LDP             X19, X9, [X23,#0x20]
LOAD:0000000000BEDC9C                 STR             X23, [X22,#0x170]
LOAD:0000000000BEDCA0                 SBFIZ           X28, X0, #3, #0x20 ; ' '
LOAD:0000000000BEDCA4                 STR             W26, [X22,#0xB8]
LOAD:0000000000BEDCA8                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEDCAC                 SUB             X27, X9, X28
LOAD:0000000000BEDCB0                 B               loc_BEDD14
LOAD:0000000000BEDCB0 ; End of function BC_FUNCC
LOAD:0000000000BEDCB0
LOAD:0000000000BEDCB4
LOAD:0000000000BEDCB4 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDCB4
LOAD:0000000000BEDCB4
LOAD:0000000000BEDCB4 ; void __fastcall BC_FUNCCW(__int64, __int64, __int64)
LOAD:0000000000BEDCB4 BC_FUNCCW
LOAD:0000000000BEDCB4
LOAD:0000000000BEDCB4 ; FUNCTION CHUNK AT LOAD:0000000000BEDE48 SIZE 00000008 BYTES
LOAD:0000000000BEDCB4
LOAD:0000000000BEDCB4                 LDR             X3, [X22,#0x158]
LOAD:0000000000BEDCB8                 ADD             X1, X27, X28
LOAD:0000000000BEDCBC                 LDR             X0, [X23,#0x30]
LOAD:0000000000BEDCC0                 ADD             X28, X19, X28
LOAD:0000000000BEDCC4                 CMP             X1, X0
LOAD:0000000000BEDCC8                 STP             X19, X28, [X23,#0x20]
LOAD:0000000000BEDCCC                 LDR             X1, [X2,#0x28]
LOAD:0000000000BEDCD0                 MOV             W8, #0xFFFFFFFE
LOAD:0000000000BEDCD4                 MOV             X0, X23
LOAD:0000000000BEDCD8                 B.HI            loc_BEDE48
LOAD:0000000000BEDCDC                 STR             W8, [X22,#0xB8]
LOAD:0000000000BEDCE0                 BLR             X3
LOAD:0000000000BEDCE4                 LDP             X19, X9, [X23,#0x20]
LOAD:0000000000BEDCE8                 STR             X23, [X22,#0x170]
LOAD:0000000000BEDCEC                 SBFIZ           X28, X0, #3, #0x20 ; ' '
LOAD:0000000000BEDCF0                 STR             W26, [X22,#0xB8]
LOAD:0000000000BEDCF4                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEDCF8                 SUB             X27, X9, X28
LOAD:0000000000BEDCFC                 B               loc_BEDD14
LOAD:0000000000BEDCFC ; End of function BC_FUNCCW
LOAD:0000000000BEDCFC
LOAD:0000000000BEDD00 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDD00 ; START OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BEDD00
LOAD:0000000000BEDD00 loc_BEDD00                              ; CODE XREF: sub_BEDE10-D8↓j
LOAD:0000000000BEDD00                 TBZ             W21, #2, loc_BEE0A8
LOAD:0000000000BEDD04                 LDUR            X21, [X17,#-8]
LOAD:0000000000BEDD08                 MOV             X8, #0xFFFEFFFFFFFFFFFF
LOAD:0000000000BEDD0C                 MOV             X19, X17
LOAD:0000000000BEDD10                 STR             X8, [X27,#-8]!
LOAD:0000000000BEDD14
LOAD:0000000000BEDD14 loc_BEDD14                              ; CODE XREF: BC_FUNCC+48↑j
LOAD:0000000000BEDD14                                         ; BC_FUNCCW+48↑j ...
LOAD:0000000000BEDD14                 ADDS            X28, X28, #8
LOAD:0000000000BEDD18                 MOV             W0, #1
LOAD:0000000000BEDD1C                 B.EQ            loc_BEDDFC
LOAD:0000000000BEDD20                 STR             W28, [SP,#arg_20]
LOAD:0000000000BEDD24                 ANDS            X0, X21, #3
LOAD:0000000000BEDD28                 B.EQ            loc_BED6A4
LOAD:0000000000BEDD2C
LOAD:0000000000BEDD2C loc_BEDD2C                              ; CODE XREF: BC_RETM+AC↑j
LOAD:0000000000BEDD2C                                         ; sub_BEDE9C+9C↓j ...
LOAD:0000000000BEDD2C                 AND             X17, X21, #0xFFFFFFFFFFFFFFF8
LOAD:0000000000BEDD30                 CMP             X0, #1
LOAD:0000000000BEDD34                 SUB             X17, X19, X17
LOAD:0000000000BEDD38                 B.NE            loc_BEDD00
LOAD:0000000000BEDD3C                 STR             X17, [X23,#0x20]
LOAD:0000000000BEDD40                 LDRSW           X1, [SP,#arg_28]
LOAD:0000000000BEDD44                 MOV             W8, #0xFFFFFFFE
LOAD:0000000000BEDD48                 SUB             X19, X19, #0x10
LOAD:0000000000BEDD4C                 SUBS            X10, X28, #8
LOAD:0000000000BEDD50                 STR             W8, [X22,#0xB8]
LOAD:0000000000BEDD54                 B.EQ            loc_BEDD68
LOAD:0000000000BEDD58
LOAD:0000000000BEDD58 loc_BEDD58                              ; CODE XREF: sub_BEDE10-AC↓j
LOAD:0000000000BEDD58                 SUBS            X10, X10, #8
LOAD:0000000000BEDD5C                 LDR             X8, [X27],#8
LOAD:0000000000BEDD60                 STR             X8, [X19],#8
LOAD:0000000000BEDD64                 B.NE            loc_BEDD58
LOAD:0000000000BEDD68
LOAD:0000000000BEDD68 loc_BEDD68                              ; CODE XREF: sub_BEDE10-BC↑j
LOAD:0000000000BEDD68                                         ; sub_BEDE10-48↓j ...
LOAD:0000000000BEDD68                 CMP             X28, X1,LSL#3
LOAD:0000000000BEDD6C                 B.NE            loc_BEDDB0
LOAD:0000000000BEDD70
LOAD:0000000000BEDD70 loc_BEDD70                              ; CODE XREF: sub_BEDE10:loc_BEDDCC↓j
LOAD:0000000000BEDD70                                         ; sub_BEDE10-38↓j
LOAD:0000000000BEDD70                 STR             X19, [X23,#0x28]
LOAD:0000000000BEDD74
LOAD:0000000000BEDD74 loc_BEDD74                              ; CODE XREF: sub_BEE02C+78↓j
LOAD:0000000000BEDD74                 LDR             X28, [SP,#arg_0]
LOAD:0000000000BEDD78                 MOV             W0, #0
LOAD:0000000000BEDD7C                 STR             X28, [X23,#0x50]
LOAD:0000000000BEDD7C ; END OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BEDD80 ; START OF FUNCTION CHUNK FOR sub_BEDDF4
LOAD:0000000000BEDD80 ;   ADDITIONAL PARENT FUNCTION sub_BEDE10
LOAD:0000000000BEDD80
LOAD:0000000000BEDD80 loc_BEDD80                              ; CODE XREF: sub_BEDDF4+18↓j
LOAD:0000000000BEDD80                                         ; LOAD:0000000000BEEA78↓j ...
LOAD:0000000000BEDD80                 LDP             X20, X19, [SP,#arg_B0]
LOAD:0000000000BEDD84                 LDP             D9, D8, [SP,#arg_60]
LOAD:0000000000BEDD88                 LDP             X22, X21, [SP,#arg_A0]
LOAD:0000000000BEDD8C                 LDP             D11, D10, [SP,#arg_50]
LOAD:0000000000BEDD90                 LDP             X24, X23, [SP,#arg_90]
LOAD:0000000000BEDD94                 LDP             D13, D12, [SP,#arg_40]
LOAD:0000000000BEDD98                 LDP             X26, X25, [SP,#arg_80]
LOAD:0000000000BEDD9C                 LDP             D15, D14, [SP,#arg_30]
LOAD:0000000000BEDDA0                 LDP             X28, X27, [SP,#arg_70]
LOAD:0000000000BEDDA4                 LDP             X29, X30, [SP,#arg_C0]
LOAD:0000000000BEDDA8                 ADD             SP, SP, #0xD0
LOAD:0000000000BEDDAC                 RET
LOAD:0000000000BEDDAC ; END OF FUNCTION CHUNK FOR sub_BEDDF4
LOAD:0000000000BEDDB0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDDB0 ; START OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BEDDB0
LOAD:0000000000BEDDB0 loc_BEDDB0                              ; CODE XREF: sub_BEDE10-A4↑j
LOAD:0000000000BEDDB0                 B.GT            loc_BEDDCC
LOAD:0000000000BEDDB4                 LDR             X2, [X23,#0x30]
LOAD:0000000000BEDDB8                 CMP             X19, X2
LOAD:0000000000BEDDBC                 B.CS            loc_BEDDDC
LOAD:0000000000BEDDC0                 STR             X26, [X19],#8
LOAD:0000000000BEDDC4                 ADD             X28, X28, #8
LOAD:0000000000BEDDC8                 B               loc_BEDD68
LOAD:0000000000BEDDCC ; ---------------------------------------------------------------------------
LOAD:0000000000BEDDCC
LOAD:0000000000BEDDCC loc_BEDDCC                              ; CODE XREF: sub_BEDE10:loc_BEDDB0↑j
LOAD:0000000000BEDDCC                 CBZ             X1, loc_BEDD70
LOAD:0000000000BEDDD0                 SUB             X0, X28, X1,LSL#3
LOAD:0000000000BEDDD4                 SUB             X19, X19, X0
LOAD:0000000000BEDDD8                 B               loc_BEDD70
LOAD:0000000000BEDDDC ; ---------------------------------------------------------------------------
LOAD:0000000000BEDDDC
LOAD:0000000000BEDDDC loc_BEDDDC                              ; CODE XREF: sub_BEDE10-54↑j
LOAD:0000000000BEDDDC                 STR             X19, [X23,#0x28]
LOAD:0000000000BEDDE0                 MOV             X0, X23
LOAD:0000000000BEDDE4                 BL              sub_BCCCCC
LOAD:0000000000BEDDE8                 LDR             X19, [X23,#0x28]
LOAD:0000000000BEDDEC                 LDRSW           X1, [SP,#arg_28]
LOAD:0000000000BEDDF0                 B               loc_BEDD68
LOAD:0000000000BEDDF0 ; END OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BEDDF4
LOAD:0000000000BEDDF4 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDDF4
LOAD:0000000000BEDDF4
LOAD:0000000000BEDDF4 sub_BEDDF4                              ; CODE XREF: sub_BC9028+17C↑p
LOAD:0000000000BEDDF4                                         ; sub_BC91F4+20↑p ...
LOAD:0000000000BEDDF4
LOAD:0000000000BEDDF4 arg_10          =  0x10
LOAD:0000000000BEDDF4 arg_30          =  0x30
LOAD:0000000000BEDDF4 arg_38          =  0x38
LOAD:0000000000BEDDF4 arg_40          =  0x40
LOAD:0000000000BEDDF4 arg_48          =  0x48
LOAD:0000000000BEDDF4 arg_50          =  0x50
LOAD:0000000000BEDDF4 arg_58          =  0x58
LOAD:0000000000BEDDF4 arg_60          =  0x60
LOAD:0000000000BEDDF4 arg_68          =  0x68
LOAD:0000000000BEDDF4 arg_70          =  0x70
LOAD:0000000000BEDDF4 arg_78          =  0x78
LOAD:0000000000BEDDF4 arg_80          =  0x80
LOAD:0000000000BEDDF4 arg_88          =  0x88
LOAD:0000000000BEDDF4 arg_90          =  0x90
LOAD:0000000000BEDDF4 arg_98          =  0x98
LOAD:0000000000BEDDF4 arg_A0          =  0xA0
LOAD:0000000000BEDDF4 arg_A8          =  0xA8
LOAD:0000000000BEDDF4 arg_B0          =  0xB0
LOAD:0000000000BEDDF4 arg_B8          =  0xB8
LOAD:0000000000BEDDF4 arg_C0          =  0xC0
LOAD:0000000000BEDDF4 arg_C8          =  0xC8
LOAD:0000000000BEDDF4
LOAD:0000000000BEDDF4 ; FUNCTION CHUNK AT LOAD:0000000000BEDD80 SIZE 00000030 BYTES
LOAD:0000000000BEDDF4
LOAD:0000000000BEDDF4                 MOV             SP, X0
LOAD:0000000000BEDDF8                 MOV             X0, X1
LOAD:0000000000BEDDFC
LOAD:0000000000BEDDFC loc_BEDDFC                              ; CODE XREF: sub_BEDE10-F4↑j
LOAD:0000000000BEDDFC                 LDR             X23, [SP,#arg_10]
LOAD:0000000000BEDE00                 MOV             W8, #0xFFFFFFFE
LOAD:0000000000BEDE04                 LDR             X22, [X23,#0x10]
LOAD:0000000000BEDE08                 STR             W8, [X22,#0xB8]
LOAD:0000000000BEDE0C                 B               loc_BEDD80
LOAD:0000000000BEDE0C ; End of function sub_BEDDF4
LOAD:0000000000BEDE0C
LOAD:0000000000BEDE10
LOAD:0000000000BEDE10 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDE10
LOAD:0000000000BEDE10
LOAD:0000000000BEDE10 ; __int64 sub_BEDE10()
LOAD:0000000000BEDE10 sub_BEDE10                              ; CODE XREF: sub_BC9028:loc_BC9100↑p
LOAD:0000000000BEDE10
LOAD:0000000000BEDE10 arg_0           =  0
LOAD:0000000000BEDE10 arg_10          =  0x10
LOAD:0000000000BEDE10 arg_20          =  0x20
LOAD:0000000000BEDE10 arg_28          =  0x28
LOAD:0000000000BEDE10
LOAD:0000000000BEDE10 ; FUNCTION CHUNK AT LOAD:0000000000BED6A4 SIZE 00000074 BYTES
LOAD:0000000000BEDE10 ; FUNCTION CHUNK AT LOAD:0000000000BEDD00 SIZE 00000080 BYTES
LOAD:0000000000BEDE10 ; FUNCTION CHUNK AT LOAD:0000000000BEDD80 SIZE 00000030 BYTES
LOAD:0000000000BEDE10 ; FUNCTION CHUNK AT LOAD:0000000000BEDDB0 SIZE 00000044 BYTES
LOAD:0000000000BEDE10 ; FUNCTION CHUNK AT LOAD:0000000000BEE0A8 SIZE 00000044 BYTES
LOAD:0000000000BEDE10 ; FUNCTION CHUNK AT LOAD:0000000000BEF430 SIZE 00000020 BYTES
LOAD:0000000000BEDE10 ; FUNCTION CHUNK AT LOAD:0000000000BEF968 SIZE 00000024 BYTES
LOAD:0000000000BEDE10
LOAD:0000000000BEDE10                 AND             SP, X0, #0xFFFFFFFFFFFFFFFC
LOAD:0000000000BEDE14                 LDR             X23, [SP,#arg_10]
LOAD:0000000000BEDE18                 MOV             X24, #0xFFF9000000000000
LOAD:0000000000BEDE1C                 MOV             X25, #0xFFF90000
LOAD:0000000000BEDE20                 MOV             X26, #0xFFFFFFFFFFFFFFFF
LOAD:0000000000BEDE24                 MOV             W28, #0x10
LOAD:0000000000BEDE28                 LDR             X19, [X23,#0x20]
LOAD:0000000000BEDE2C                 LDR             X22, [X23,#0x10]
LOAD:0000000000BEDE30                 MOV             X8, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEDE34                 SUB             X27, X19, #8
LOAD:0000000000BEDE38                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEDE3C                 STUR            X8, [X19,#-8]
LOAD:0000000000BEDE40                 STR             W26, [X22,#0xB8]
LOAD:0000000000BEDE44                 B               loc_BEDD14
LOAD:0000000000BEDE44 ; End of function sub_BEDE10
LOAD:0000000000BEDE44
LOAD:0000000000BEDE48 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDE48 ; START OF FUNCTION CHUNK FOR BC_FUNCC
LOAD:0000000000BEDE48 ;   ADDITIONAL PARENT FUNCTION BC_FUNCCW
LOAD:0000000000BEDE48
LOAD:0000000000BEDE48 loc_BEDE48                              ; CODE XREF: BC_FUNCC+24↑j
LOAD:0000000000BEDE48                                         ; BC_FUNCCW+24↑j
LOAD:0000000000BEDE48                 MOV             W1, #0x14
LOAD:0000000000BEDE4C                 B               loc_BEDE68
LOAD:0000000000BEDE4C ; END OF FUNCTION CHUNK FOR BC_FUNCC
LOAD:0000000000BEDE50 ; ---------------------------------------------------------------------------
LOAD:0000000000BEDE50 ; START OF FUNCTION CHUNK FOR BC_FUNCF
LOAD:0000000000BEDE50 ;   ADDITIONAL PARENT FUNCTION BC_IFUNCF
LOAD:0000000000BEDE50 ;   ADDITIONAL PARENT FUNCTION BC_JFUNCF
LOAD:0000000000BEDE50
LOAD:0000000000BEDE50 loc_BEDE50                              ; CODE XREF: BC_FUNCF+2C↑j
LOAD:0000000000BEDE50                                         ; BC_IFUNCF+10↑j ...
LOAD:0000000000BEDE50                 ADD             X28, X19, X28
LOAD:0000000000BEDE54                 SUB             X27, X27, X19
LOAD:0000000000BEDE58                 MOV             X0, X23
LOAD:0000000000BEDE5C                 STP             X19, X28, [X23,#0x20]
LOAD:0000000000BEDE60                 ADD             X21, X21, #4
LOAD:0000000000BEDE64                 LSR             X1, X27, #3
LOAD:0000000000BEDE68
LOAD:0000000000BEDE68 loc_BEDE68                              ; CODE XREF: BC_FUNCC+1E4↑j
LOAD:0000000000BEDE68                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEDE6C                 BL              sub_BCCCCC
LOAD:0000000000BEDE70                 LDP             X19, X28, [X23,#0x20]
LOAD:0000000000BEDE74                 LDUR            X2, [X19,#-0x10]
LOAD:0000000000BEDE78                 SUB             X28, X28, X19
LOAD:0000000000BEDE7C                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEDE80                 LDR             X21, [X2,#0x20]
LOAD:0000000000BEDE84                 LDR             W16, [X21],#4
LOAD:0000000000BEDE88                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEDE8C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEDE90                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEDE94                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BEDE98                 BR              X8
LOAD:0000000000BEDE98 ; END OF FUNCTION CHUNK FOR BC_FUNCF
LOAD:0000000000BEDE9C
LOAD:0000000000BEDE9C ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDE9C
LOAD:0000000000BEDE9C ; Attributes: bp-based frame
LOAD:0000000000BEDE9C
LOAD:0000000000BEDE9C sub_BEDE9C                              ; CODE XREF: lua_resume+D0↑j
LOAD:0000000000BEDE9C                                         ; LOAD:0000000000BEE8B0↓p ...
LOAD:0000000000BEDE9C
LOAD:0000000000BEDE9C var_BF          = -0xBF
LOAD:0000000000BEDE9C var_B8          = -0xB8
LOAD:0000000000BEDE9C var_B0          = -0xB0
LOAD:0000000000BEDE9C var_A0          = -0xA0
LOAD:0000000000BEDE9C var_9C          = -0x9C
LOAD:0000000000BEDE9C var_98          = -0x98
LOAD:0000000000BEDE9C var_90          = -0x90
LOAD:0000000000BEDE9C var_88          = -0x88
LOAD:0000000000BEDE9C var_80          = -0x80
LOAD:0000000000BEDE9C var_78          = -0x78
LOAD:0000000000BEDE9C var_70          = -0x70
LOAD:0000000000BEDE9C var_68          = -0x68
LOAD:0000000000BEDE9C var_60          = -0x60
LOAD:0000000000BEDE9C var_58          = -0x58
LOAD:0000000000BEDE9C var_50          = -0x50
LOAD:0000000000BEDE9C var_48          = -0x48
LOAD:0000000000BEDE9C var_40          = -0x40
LOAD:0000000000BEDE9C var_38          = -0x38
LOAD:0000000000BEDE9C var_30          = -0x30
LOAD:0000000000BEDE9C var_28          = -0x28
LOAD:0000000000BEDE9C var_20          = -0x20
LOAD:0000000000BEDE9C var_18          = -0x18
LOAD:0000000000BEDE9C var_10          = -0x10
LOAD:0000000000BEDE9C var_8           = -8
LOAD:0000000000BEDE9C var_s0          =  0
LOAD:0000000000BEDE9C var_s8          =  8
LOAD:0000000000BEDE9C
LOAD:0000000000BEDE9C ; FUNCTION CHUNK AT LOAD:0000000000BED6A4 SIZE 00000074 BYTES
LOAD:0000000000BEDE9C
LOAD:0000000000BEDE9C                 SUB             SP, SP, #0xD0
LOAD:0000000000BEDEA0                 STP             X29, X30, [SP,#0xC0+var_s0]
LOAD:0000000000BEDEA4                 ADD             X29, SP, #0xC0
LOAD:0000000000BEDEA8                 STP             X20, X19, [SP,#0xC0+var_10]
LOAD:0000000000BEDEAC                 STP             D9, D8, [SP,#0xC0+var_60]
LOAD:0000000000BEDEB0                 STP             X22, X21, [SP,#0xC0+var_20]
LOAD:0000000000BEDEB4                 STP             D11, D10, [SP,#0xC0+var_70]
LOAD:0000000000BEDEB8                 STP             X24, X23, [SP,#0xC0+var_30]
LOAD:0000000000BEDEBC                 STP             D13, D12, [SP,#0xC0+var_80]
LOAD:0000000000BEDEC0                 STP             X26, X25, [SP,#0xC0+var_40]
LOAD:0000000000BEDEC4                 STP             D15, D14, [SP,#0xC0+var_90]
LOAD:0000000000BEDEC8                 STP             X28, X27, [SP,#0xC0+var_50]
LOAD:0000000000BEDECC                 MOV             X23, X0
LOAD:0000000000BEDED0                 LDR             X22, [X23,#0x10]
LOAD:0000000000BEDED4                 MOV             X19, X1
LOAD:0000000000BEDED8                 STR             X23, [SP,#0xC0+var_B0]
LOAD:0000000000BEDEDC                 MOV             W21, #5
LOAD:0000000000BEDEE0                 STR             WZR, [SP,#0xC0+var_98]
LOAD:0000000000BEDEE4                 ADD             X8, SP, #0xC0+var_BF
LOAD:0000000000BEDEE8                 LDRB            W9, [X23,#0xB]
LOAD:0000000000BEDEEC                 STR             WZR, [SP,#0xC0+var_9C]
LOAD:0000000000BEDEF0                 STR             X23, [SP,#0xC0+var_B8]
LOAD:0000000000BEDEF4                 STR             XZR, [SP]
LOAD:0000000000BEDEF8                 STR             X8, [X23,#0x50]
LOAD:0000000000BEDEFC                 CBZ             W9, loc_BEDFD4
LOAD:0000000000BEDF00                 STR             X23, [X22,#0x170]
LOAD:0000000000BEDF04                 MOV             X27, X19
LOAD:0000000000BEDF08                 LDP             X19, X0, [X23,#0x20]
LOAD:0000000000BEDF0C                 MOV             X24, #0xFFF9000000000000
LOAD:0000000000BEDF10                 MOV             X25, #0xFFF90000
LOAD:0000000000BEDF14                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEDF18                 STRB            WZR, [X23,#0xB]
LOAD:0000000000BEDF1C                 MOV             X26, #0xFFFFFFFFFFFFFFFF
LOAD:0000000000BEDF20                 SUB             X28, X0, X19
LOAD:0000000000BEDF24                 ANDS            X0, X21, #3
LOAD:0000000000BEDF28                 ADD             X28, X28, #8
LOAD:0000000000BEDF2C                 STR             W26, [X22,#0xB8]
LOAD:0000000000BEDF30                 STR             W28, [SP,#0xC0+var_A0]
LOAD:0000000000BEDF34                 B.EQ            loc_BED6A4
LOAD:0000000000BEDF38                 B               loc_BEDD2C
LOAD:0000000000BEDF38 ; End of function sub_BEDE9C
LOAD:0000000000BEDF38
LOAD:0000000000BEDF3C
LOAD:0000000000BEDF3C ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDF3C
LOAD:0000000000BEDF3C ; Attributes: bp-based frame
LOAD:0000000000BEDF3C
LOAD:0000000000BEDF3C ; __int64 sub_BEDF3C(void)
LOAD:0000000000BEDF3C sub_BEDF3C                              ; CODE XREF: lua_pcall+94↑p
LOAD:0000000000BEDF3C                                         ; sub_BEFD28+90↓p ...
LOAD:0000000000BEDF3C
LOAD:0000000000BEDF3C var_9C          = -0x9C
LOAD:0000000000BEDF3C var_90          = -0x90
LOAD:0000000000BEDF3C var_88          = -0x88
LOAD:0000000000BEDF3C var_80          = -0x80
LOAD:0000000000BEDF3C var_78          = -0x78
LOAD:0000000000BEDF3C var_70          = -0x70
LOAD:0000000000BEDF3C var_68          = -0x68
LOAD:0000000000BEDF3C var_60          = -0x60
LOAD:0000000000BEDF3C var_58          = -0x58
LOAD:0000000000BEDF3C var_50          = -0x50
LOAD:0000000000BEDF3C var_48          = -0x48
LOAD:0000000000BEDF3C var_40          = -0x40
LOAD:0000000000BEDF3C var_38          = -0x38
LOAD:0000000000BEDF3C var_30          = -0x30
LOAD:0000000000BEDF3C var_28          = -0x28
LOAD:0000000000BEDF3C var_20          = -0x20
LOAD:0000000000BEDF3C var_18          = -0x18
LOAD:0000000000BEDF3C var_10          = -0x10
LOAD:0000000000BEDF3C var_8           = -8
LOAD:0000000000BEDF3C var_s0          =  0
LOAD:0000000000BEDF3C var_s8          =  8
LOAD:0000000000BEDF3C
LOAD:0000000000BEDF3C                 SUB             SP, SP, #0xD0
LOAD:0000000000BEDF40                 STP             X29, X30, [SP,#0xC0+var_s0]
LOAD:0000000000BEDF44                 ADD             X29, SP, #0xC0
LOAD:0000000000BEDF48                 STP             X20, X19, [SP,#0xC0+var_10]
LOAD:0000000000BEDF4C                 STP             D9, D8, [SP,#0xC0+var_60]
LOAD:0000000000BEDF50                 STP             X22, X21, [SP,#0xC0+var_20]
LOAD:0000000000BEDF54                 STP             D11, D10, [SP,#0xC0+var_70]
LOAD:0000000000BEDF58                 STP             X24, X23, [SP,#0xC0+var_30]
LOAD:0000000000BEDF5C                 STP             D13, D12, [SP,#0xC0+var_80]
LOAD:0000000000BEDF60                 STP             X26, X25, [SP,#0xC0+var_40]
LOAD:0000000000BEDF64                 STP             D15, D14, [SP,#0xC0+var_90]
LOAD:0000000000BEDF68                 STP             X28, X27, [SP,#0xC0+var_50]
LOAD:0000000000BEDF6C                 MOV             W21, #5
LOAD:0000000000BEDF70                 STR             W3, [SP,#0xC0+var_9C]
LOAD:0000000000BEDF74                 B               loc_BEDFAC
LOAD:0000000000BEDF74 ; End of function sub_BEDF3C
LOAD:0000000000BEDF74
LOAD:0000000000BEDF78
LOAD:0000000000BEDF78 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEDF78
LOAD:0000000000BEDF78 ; Attributes: bp-based frame
LOAD:0000000000BEDF78
LOAD:0000000000BEDF78 ; __int64 __fastcall sub_BEDF78(_QWORD)
LOAD:0000000000BEDF78 sub_BEDF78                              ; CODE XREF: sub_BC924C+190↑p
LOAD:0000000000BEDF78                                         ; lua_equal+234↑p ...
LOAD:0000000000BEDF78
LOAD:0000000000BEDF78 var_C0          = -0xC0
LOAD:0000000000BEDF78 var_B8          = -0xB8
LOAD:0000000000BEDF78 var_B0          = -0xB0
LOAD:0000000000BEDF78 var_98          = -0x98
LOAD:0000000000BEDF78 var_90          = -0x90
LOAD:0000000000BEDF78 var_88          = -0x88
LOAD:0000000000BEDF78 var_80          = -0x80
LOAD:0000000000BEDF78 var_78          = -0x78
LOAD:0000000000BEDF78 var_70          = -0x70
LOAD:0000000000BEDF78 var_68          = -0x68
LOAD:0000000000BEDF78 var_60          = -0x60
LOAD:0000000000BEDF78 var_58          = -0x58
LOAD:0000000000BEDF78 var_50          = -0x50
LOAD:0000000000BEDF78 var_48          = -0x48
LOAD:0000000000BEDF78 var_40          = -0x40
LOAD:0000000000BEDF78 var_38          = -0x38
LOAD:0000000000BEDF78 var_30          = -0x30
LOAD:0000000000BEDF78 var_28          = -0x28
LOAD:0000000000BEDF78 var_20          = -0x20
LOAD:0000000000BEDF78 var_18          = -0x18
LOAD:0000000000BEDF78 var_10          = -0x10
LOAD:0000000000BEDF78 var_8           = -8
LOAD:0000000000BEDF78 var_s0          =  0
LOAD:0000000000BEDF78 var_s8          =  8
LOAD:0000000000BEDF78
LOAD:0000000000BEDF78 ; FUNCTION CHUNK AT LOAD:0000000000BEE3E0 SIZE 00000044 BYTES
LOAD:0000000000BEDF78
LOAD:0000000000BEDF78                 SUB             SP, SP, #0xD0
LOAD:0000000000BEDF7C                 STP             X29, X30, [SP,#0xC0+var_s0]
LOAD:0000000000BEDF80                 ADD             X29, SP, #0xC0
LOAD:0000000000BEDF84                 STP             X20, X19, [SP,#0xC0+var_10]
LOAD:0000000000BEDF88                 STP             D9, D8, [SP,#0xC0+var_60]
LOAD:0000000000BEDF8C                 STP             X22, X21, [SP,#0xC0+var_20]
LOAD:0000000000BEDF90                 STP             D11, D10, [SP,#0xC0+var_70]
LOAD:0000000000BEDF94                 STP             X24, X23, [SP,#0xC0+var_30]
LOAD:0000000000BEDF98                 STP             D13, D12, [SP,#0xC0+var_80]
LOAD:0000000000BEDF9C                 STP             X26, X25, [SP,#0xC0+var_40]
LOAD:0000000000BEDFA0                 STP             D15, D14, [SP,#0xC0+var_90]
LOAD:0000000000BEDFA4                 STP             X28, X27, [SP,#0xC0+var_50]
LOAD:0000000000BEDFA8                 MOV             W21, #1
LOAD:0000000000BEDFAC
LOAD:0000000000BEDFAC loc_BEDFAC                              ; CODE XREF: sub_BEDF3C+38↑j
LOAD:0000000000BEDFAC                 LDR             X28, [X0,#0x50]
LOAD:0000000000BEDFB0                 STR             W2, [SP,#0xC0+var_98]
LOAD:0000000000BEDFB4                 MOV             X23, X0
LOAD:0000000000BEDFB8                 STR             X0, [SP,#0xC0+var_B0]
LOAD:0000000000BEDFBC                 LDR             X22, [X23,#0x10]
LOAD:0000000000BEDFC0                 MOV             X19, X1
LOAD:0000000000BEDFC4                 STR             X0, [SP,#0xC0+var_B8]
LOAD:0000000000BEDFC8                 MOV             X8, SP
LOAD:0000000000BEDFCC                 STR             X28, [SP,#0xC0+var_C0]
LOAD:0000000000BEDFD0                 STR             X8, [X23,#0x50]
LOAD:0000000000BEDFD4
LOAD:0000000000BEDFD4 loc_BEDFD4                              ; CODE XREF: sub_BEDE9C+60↑j
LOAD:0000000000BEDFD4                                         ; sub_BEE02C+74↓j
LOAD:0000000000BEDFD4                 STR             X23, [X22,#0x170]
LOAD:0000000000BEDFD8                 LDP             X17, X0, [X23,#0x20]
LOAD:0000000000BEDFDC                 MOV             X24, #0xFFF9000000000000
LOAD:0000000000BEDFE0                 MOV             X25, #0xFFF90000
LOAD:0000000000BEDFE4                 ADD             X21, X21, X19
LOAD:0000000000BEDFE8                 MOV             X26, #0xFFFFFFFFFFFFFFFF
LOAD:0000000000BEDFEC                 SUB             X21, X21, X17
LOAD:0000000000BEDFF0                 SUB             X28, X0, X19
LOAD:0000000000BEDFF4                 STR             W26, [X22,#0xB8]
LOAD:0000000000BEDFF8
LOAD:0000000000BEDFF8 loc_BEDFF8                              ; CODE XREF: BC_ISLT+27B4↓j
LOAD:0000000000BEDFF8                                         ; LOAD:0000000000BEE7C0↓j ...
LOAD:0000000000BEDFF8                 LDUR            X2, [X19,#-0x10]
LOAD:0000000000BEDFFC                 ASR             X15, X2, #0x2F ; '/'
LOAD:0000000000BEE000                 CMN             X15, #9
LOAD:0000000000BEE004                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEE008                 B.NE            loc_BEE3E0
LOAD:0000000000BEE00C
LOAD:0000000000BEE00C loc_BEE00C                              ; CODE XREF: BC_GGET+140C↓j
LOAD:0000000000BEE00C                                         ; BC_GGET+14C0↓j
LOAD:0000000000BEE00C                 STUR            X21, [X19,#-8]
LOAD:0000000000BEE010                 LDR             X21, [X2,#0x20]
LOAD:0000000000BEE014                 LDR             W16, [X21],#4
LOAD:0000000000BEE018                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEE01C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEE020                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEE024                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BEE028                 BR              X8
LOAD:0000000000BEE028 ; End of function sub_BEDF78
LOAD:0000000000BEE028
LOAD:0000000000BEE02C
LOAD:0000000000BEE02C ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEE02C
LOAD:0000000000BEE02C ; Attributes: bp-based frame
LOAD:0000000000BEE02C
LOAD:0000000000BEE02C ; void __fastcall sub_BEE02C(_QWORD *, __int64, __int64, __int64 (__fastcall *)(_QWORD *, __int64, __int64))
LOAD:0000000000BEE02C sub_BEE02C                              ; CODE XREF: lua_newstate+140↑p
LOAD:0000000000BEE02C                                         ; lua_close+8C↑p ...
LOAD:0000000000BEE02C
LOAD:0000000000BEE02C var_C0          = -0xC0
LOAD:0000000000BEE02C var_B8          = -0xB8
LOAD:0000000000BEE02C var_B0          = -0xB0
LOAD:0000000000BEE02C var_9C          = -0x9C
LOAD:0000000000BEE02C var_98          = -0x98
LOAD:0000000000BEE02C var_90          = -0x90
LOAD:0000000000BEE02C var_88          = -0x88
LOAD:0000000000BEE02C var_80          = -0x80
LOAD:0000000000BEE02C var_78          = -0x78
LOAD:0000000000BEE02C var_70          = -0x70
LOAD:0000000000BEE02C var_68          = -0x68
LOAD:0000000000BEE02C var_60          = -0x60
LOAD:0000000000BEE02C var_58          = -0x58
LOAD:0000000000BEE02C var_50          = -0x50
LOAD:0000000000BEE02C var_48          = -0x48
LOAD:0000000000BEE02C var_40          = -0x40
LOAD:0000000000BEE02C var_38          = -0x38
LOAD:0000000000BEE02C var_30          = -0x30
LOAD:0000000000BEE02C var_28          = -0x28
LOAD:0000000000BEE02C var_20          = -0x20
LOAD:0000000000BEE02C var_18          = -0x18
LOAD:0000000000BEE02C var_10          = -0x10
LOAD:0000000000BEE02C var_8           = -8
LOAD:0000000000BEE02C var_s0          =  0
LOAD:0000000000BEE02C var_s8          =  8
LOAD:0000000000BEE02C
LOAD:0000000000BEE02C                 SUB             SP, SP, #0xD0
LOAD:0000000000BEE030                 STP             X29, X30, [SP,#0xC0+var_s0]
LOAD:0000000000BEE034                 ADD             X29, SP, #0xC0
LOAD:0000000000BEE038                 STP             X20, X19, [SP,#0xC0+var_10]
LOAD:0000000000BEE03C                 STP             D9, D8, [SP,#0xC0+var_60]
LOAD:0000000000BEE040                 STP             X22, X21, [SP,#0xC0+var_20]
LOAD:0000000000BEE044                 STP             D11, D10, [SP,#0xC0+var_70]
LOAD:0000000000BEE048                 STP             X24, X23, [SP,#0xC0+var_30]
LOAD:0000000000BEE04C                 STP             D13, D12, [SP,#0xC0+var_80]
LOAD:0000000000BEE050                 STP             X26, X25, [SP,#0xC0+var_40]
LOAD:0000000000BEE054                 STP             D15, D14, [SP,#0xC0+var_90]
LOAD:0000000000BEE058                 STP             X28, X27, [SP,#0xC0+var_50]
LOAD:0000000000BEE05C                 MOV             X23, X0
LOAD:0000000000BEE060                 LDR             X27, [X0,#0x38]
LOAD:0000000000BEE064                 STR             X0, [SP,#0xC0+var_B0]
LOAD:0000000000BEE068                 LDR             X22, [X23,#0x10]
LOAD:0000000000BEE06C                 LDR             X17, [X23,#0x28]
LOAD:0000000000BEE070                 STR             X0, [SP,#0xC0+var_B8]
LOAD:0000000000BEE074                 LDR             X28, [X23,#0x50]
LOAD:0000000000BEE078                 SUB             X27, X27, X17
LOAD:0000000000BEE07C                 STR             W27, [SP,#0xC0+var_98]
LOAD:0000000000BEE080                 STR             WZR, [SP,#0xC0+var_9C]
LOAD:0000000000BEE084                 MOV             X8, SP
LOAD:0000000000BEE088                 STR             X28, [SP,#0xC0+var_C0]
LOAD:0000000000BEE08C                 STR             X8, [X23,#0x50]
LOAD:0000000000BEE090                 STR             X23, [X22,#0x170]
LOAD:0000000000BEE094                 BLR             X3
LOAD:0000000000BEE098                 MOV             X19, X0
LOAD:0000000000BEE09C                 MOV             W21, #5
LOAD:0000000000BEE0A0                 CBNZ            X19, loc_BEDFD4
LOAD:0000000000BEE0A4                 B               loc_BEDD74
LOAD:0000000000BEE0A4 ; End of function sub_BEE02C
LOAD:0000000000BEE0A4
LOAD:0000000000BEE0A8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE0A8 ; START OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BEE0A8
LOAD:0000000000BEE0A8 loc_BEE0A8                              ; CODE XREF: sub_BEDE10:loc_BEDD00↑j
LOAD:0000000000BEE0A8                 LDUR            X2, [X17,#-0x10]
LOAD:0000000000BEE0AC                 LDUR            X0, [X19,#-0x20]
LOAD:0000000000BEE0B0                 MOV             X3, X19
LOAD:0000000000BEE0B4                 MOV             X19, X17
LOAD:0000000000BEE0B8                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEE0BC                 CMP             X0, #1
LOAD:0000000000BEE0C0                 LDUR            X21, [X3,#-0x18]
LOAD:0000000000BEE0C4                 ADD             X8, X27, X28
LOAD:0000000000BEE0C8                 STUR            X26, [X8,#-8]
LOAD:0000000000BEE0CC                 B.LS            loc_BEE0DC
LOAD:0000000000BEE0D0                 LDR             X2, [X2,#0x20]
LOAD:0000000000BEE0D4                 LDUR            X20, [X2,#-0x48]
LOAD:0000000000BEE0D8                 BR              X0
LOAD:0000000000BEE0DC ; ---------------------------------------------------------------------------
LOAD:0000000000BEE0DC
LOAD:0000000000BEE0DC loc_BEE0DC                              ; CODE XREF: sub_BEDE10+2BC↑j
LOAD:0000000000BEE0DC                 B.EQ            loc_BEF968
LOAD:0000000000BEE0E0                 SUB             X3, X3, #0x20 ; ' '
LOAD:0000000000BEE0E4                 SUB             X28, X3, X19
LOAD:0000000000BEE0E8                 B               loc_BEF430
LOAD:0000000000BEE0E8 ; END OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BEE0EC
LOAD:0000000000BEE0EC ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEE0EC
LOAD:0000000000BEE0EC
LOAD:0000000000BEE0EC sub_BEE0EC                              ; DATA XREF: sub_BF4358+374↓o
LOAD:0000000000BEE0EC                                         ; sub_BFBFA4+220↓o ...
LOAD:0000000000BEE0EC
LOAD:0000000000BEE0EC arg_8           =  8
LOAD:0000000000BEE0EC
LOAD:0000000000BEE0EC ; FUNCTION CHUNK AT LOAD:0000000000BEC9DC SIZE 00000038 BYTES
LOAD:0000000000BEE0EC ; FUNCTION CHUNK AT LOAD:0000000000BEE3B0 SIZE 00000018 BYTES
LOAD:0000000000BEE0EC
LOAD:0000000000BEE0EC                 LDUR            W16, [X21,#-4]
LOAD:0000000000BEE0F0                 SUB             X1, X3, #0x20 ; ' '
LOAD:0000000000BEE0F4                 LDR             X8, [X27]
LOAD:0000000000BEE0F8                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE0FC                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEE100                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEE104                 ADD             X9, X19, X17,LSL#3
LOAD:0000000000BEE108                 SUBS            X9, X1, X9
LOAD:0000000000BEE10C                 B.EQ            loc_BEE11C
LOAD:0000000000BEE110                 STR             X8, [X1]
LOAD:0000000000BEE114                 LSR             X2, X9, #3
LOAD:0000000000BEE118                 B               loc_BEC9DC
LOAD:0000000000BEE11C ; ---------------------------------------------------------------------------
LOAD:0000000000BEE11C
LOAD:0000000000BEE11C loc_BEE11C                              ; CODE XREF: sub_BEE0EC+20↑j
LOAD:0000000000BEE11C                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEE120                 B               loc_BEE2C4
LOAD:0000000000BEE120 ; End of function sub_BEE0EC
LOAD:0000000000BEE120
LOAD:0000000000BEE124 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE124 ; START OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BEE124
LOAD:0000000000BEE124 loc_BEE124                              ; CODE XREF: BC_GGET+DC↑j
LOAD:0000000000BEE124                 MOV             X3, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEE128                 ADD             X1, X19, X17,LSL#3
LOAD:0000000000BEE12C                 ADD             X3, X28, X3,LSL#47
LOAD:0000000000BEE130                 B               loc_BEE140
LOAD:0000000000BEE134 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE134
LOAD:0000000000BEE134 loc_BEE134                              ; CODE XREF: BC_GGET+14C↑j
LOAD:0000000000BEE134                 MOVK            X1, #0xFFFA,LSL#48
LOAD:0000000000BEE138                 STR             X1, [X22,#0xE8]
LOAD:0000000000BEE13C                 ADD             X1, X22, #0xE8
LOAD:0000000000BEE140
LOAD:0000000000BEE140 loc_BEE140                              ; CODE XREF: BC_GGET+1384↑j
LOAD:0000000000BEE140                 ADD             X2, SP, #arg_18
LOAD:0000000000BEE144                 STR             X3, [SP,#arg_18]
LOAD:0000000000BEE148                 B               loc_BEE168
LOAD:0000000000BEE148 ; END OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BEE14C ; ---------------------------------------------------------------------------
LOAD:0000000000BEE14C ; START OF FUNCTION CHUNK FOR BC_TGETB
LOAD:0000000000BEE14C
LOAD:0000000000BEE14C loc_BEE14C                              ; CODE XREF: BC_TGETB+18↑j
LOAD:0000000000BEE14C                                         ; BC_TGETB+2C↑j ...
LOAD:0000000000BEE14C                 ADD             X28, X28, X24
LOAD:0000000000BEE150                 ADD             X1, X19, X17,LSL#3
LOAD:0000000000BEE154                 ADD             X2, SP, #arg_18
LOAD:0000000000BEE158                 STR             X28, [SP,#arg_18]
LOAD:0000000000BEE15C                 B               loc_BEE168
LOAD:0000000000BEE15C ; END OF FUNCTION CHUNK FOR BC_TGETB
LOAD:0000000000BEE160 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE160 ; START OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BEE160
LOAD:0000000000BEE160 loc_BEE160                              ; CODE XREF: BC_GGET+4C↑j
LOAD:0000000000BEE160                                         ; BC_GGET+68↑j ...
LOAD:0000000000BEE160                 ADD             X1, X19, X17,LSL#3
LOAD:0000000000BEE164                 ADD             X2, X19, X28,LSL#3
LOAD:0000000000BEE168
LOAD:0000000000BEE168 loc_BEE168                              ; CODE XREF: BC_GGET+139C↑j
LOAD:0000000000BEE168                                         ; BC_TGETB+1260↑j
LOAD:0000000000BEE168                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE16C                 MOV             X0, X23
LOAD:0000000000BEE170                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEE174                 BL              sub_BF3C38
LOAD:0000000000BEE178                 CBZ             X0, loc_BEE19C
LOAD:0000000000BEE17C                 LDR             X8, [X0]
LOAD:0000000000BEE180                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEE184                 LDR             W16, [X21],#4
LOAD:0000000000BEE188                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEE18C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEE190                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEE194                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEE198                 BR              X8
LOAD:0000000000BEE19C ; ---------------------------------------------------------------------------
LOAD:0000000000BEE19C
LOAD:0000000000BEE19C loc_BEE19C                              ; CODE XREF: BC_GGET+13CC↑j
LOAD:0000000000BEE19C                 SUB             X9, X19, #2
LOAD:0000000000BEE1A0                 LDR             X19, [X23,#0x28]
LOAD:0000000000BEE1A4                 MOV             W28, #0x10
LOAD:0000000000BEE1A8                 LDUR            X2, [X19,#-0x10]
LOAD:0000000000BEE1AC                 STUR            X21, [X19,#-0x18]
LOAD:0000000000BEE1B0                 SUB             X21, X19, X9
LOAD:0000000000BEE1B4                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEE1B8                 B               loc_BEE00C
LOAD:0000000000BEE1B8 ; END OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BEE1BC ; ---------------------------------------------------------------------------
LOAD:0000000000BEE1BC ; START OF FUNCTION CHUNK FOR BC_TGETR
LOAD:0000000000BEE1BC
LOAD:0000000000BEE1BC loc_BEE1BC                              ; CODE XREF: BC_TGETR+24↑j
LOAD:0000000000BEE1BC                 SXTW            X1, W9
LOAD:0000000000BEE1C0                 BL              sub_BF2E24
LOAD:0000000000BEE1C4                 MOV             X8, X26
LOAD:0000000000BEE1C8                 CBZ             X0, loc_BECF94
LOAD:0000000000BEE1CC                 LDR             X8, [X0]
LOAD:0000000000BEE1D0                 B               loc_BECF94
LOAD:0000000000BEE1D0 ; END OF FUNCTION CHUNK FOR BC_TGETR
LOAD:0000000000BEE1D4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE1D4 ; START OF FUNCTION CHUNK FOR BC_TSETS
LOAD:0000000000BEE1D4
LOAD:0000000000BEE1D4 loc_BEE1D4                              ; CODE XREF: BC_TSETS+20↑j
LOAD:0000000000BEE1D4                 MOV             X3, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEE1D8                 ADD             X1, X19, X17,LSL#3
LOAD:0000000000BEE1DC                 ADD             X3, X28, X3,LSL#47
LOAD:0000000000BEE1E0                 B               loc_BEE1F0
LOAD:0000000000BEE1E0 ; END OF FUNCTION CHUNK FOR BC_TSETS
LOAD:0000000000BEE1E4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE1E4 ; START OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BEE1E4
LOAD:0000000000BEE1E4 loc_BEE1E4                              ; CODE XREF: BC_GGET+34C↑j
LOAD:0000000000BEE1E4                                         ; BC_GGET+364↑j
LOAD:0000000000BEE1E4                 MOVK            X1, #0xFFFA,LSL#48
LOAD:0000000000BEE1E8                 STR             X1, [X22,#0xE8]
LOAD:0000000000BEE1EC                 ADD             X1, X22, #0xE8
LOAD:0000000000BEE1F0
LOAD:0000000000BEE1F0 loc_BEE1F0                              ; CODE XREF: BC_TSETS+1180↑j
LOAD:0000000000BEE1F0                 ADD             X2, SP, #arg_18
LOAD:0000000000BEE1F4                 STR             X3, [SP,#arg_18]
LOAD:0000000000BEE1F8                 B               loc_BEE218
LOAD:0000000000BEE1F8 ; END OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BEE1FC ; ---------------------------------------------------------------------------
LOAD:0000000000BEE1FC ; START OF FUNCTION CHUNK FOR BC_TSETB
LOAD:0000000000BEE1FC
LOAD:0000000000BEE1FC loc_BEE1FC                              ; CODE XREF: BC_TSETB+18↑j
LOAD:0000000000BEE1FC                                         ; BC_TSETB+2C↑j ...
LOAD:0000000000BEE1FC                 ADD             X28, X28, X24
LOAD:0000000000BEE200                 ADD             X1, X19, X17,LSL#3
LOAD:0000000000BEE204                 ADD             X2, SP, #arg_18
LOAD:0000000000BEE208                 STR             X28, [SP,#arg_18]
LOAD:0000000000BEE20C                 B               loc_BEE218
LOAD:0000000000BEE20C ; END OF FUNCTION CHUNK FOR BC_TSETB
LOAD:0000000000BEE210 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE210 ; START OF FUNCTION CHUNK FOR BC_TSETV
LOAD:0000000000BEE210
LOAD:0000000000BEE210 loc_BEE210                              ; CODE XREF: BC_TSETV+1C↑j
LOAD:0000000000BEE210                                         ; BC_TSETV+38↑j ...
LOAD:0000000000BEE210                 ADD             X1, X19, X17,LSL#3
LOAD:0000000000BEE214                 ADD             X2, X19, X28,LSL#3
LOAD:0000000000BEE214 ; END OF FUNCTION CHUNK FOR BC_TSETV
LOAD:0000000000BEE218 ; START OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BEE218 ;   ADDITIONAL PARENT FUNCTION BC_TSETB
LOAD:0000000000BEE218
LOAD:0000000000BEE218 loc_BEE218                              ; CODE XREF: BC_GGET+144C↑j
LOAD:0000000000BEE218                                         ; BC_TSETB+10B0↑j
LOAD:0000000000BEE218                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE21C                 MOV             X0, X23
LOAD:0000000000BEE220                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEE224                 BL              sub_BF3DEC
LOAD:0000000000BEE228                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEE22C                 CBZ             X0, loc_BEE24C
LOAD:0000000000BEE230                 STR             X8, [X0]
LOAD:0000000000BEE234                 LDR             W16, [X21],#4
LOAD:0000000000BEE238                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEE23C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEE240                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEE244                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEE248                 BR              X8
LOAD:0000000000BEE24C ; ---------------------------------------------------------------------------
LOAD:0000000000BEE24C
LOAD:0000000000BEE24C loc_BEE24C                              ; CODE XREF: BC_GGET+1480↑j
LOAD:0000000000BEE24C                 SUB             X9, X19, #2
LOAD:0000000000BEE250                 LDR             X19, [X23,#0x28]
LOAD:0000000000BEE254                 MOV             W28, #0x18
LOAD:0000000000BEE258                 LDUR            X2, [X19,#-0x10]
LOAD:0000000000BEE25C                 STR             X8, [X19,#0x10]
LOAD:0000000000BEE260                 STUR            X21, [X19,#-0x18]
LOAD:0000000000BEE264                 SUB             X21, X19, X9
LOAD:0000000000BEE268                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEE26C                 B               loc_BEE00C
LOAD:0000000000BEE26C ; END OF FUNCTION CHUNK FOR BC_GGET
LOAD:0000000000BEE270 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE270 ; START OF FUNCTION CHUNK FOR BC_TSETR
LOAD:0000000000BEE270
LOAD:0000000000BEE270 loc_BEE270                              ; CODE XREF: BC_TSETR+2C↑j
LOAD:0000000000BEE270                 SXTW            X2, W9
LOAD:0000000000BEE274                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE278                 MOV             X0, X23
LOAD:0000000000BEE27C                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEE280                 BL              sub_BF288C
LOAD:0000000000BEE284                 B               loc_BED2B0
LOAD:0000000000BEE284 ; END OF FUNCTION CHUNK FOR BC_TSETR
LOAD:0000000000BEE288 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE288 ; START OF FUNCTION CHUNK FOR BC_ISLT
LOAD:0000000000BEE288 ;   ADDITIONAL PARENT FUNCTION BC_ISGE
LOAD:0000000000BEE288 ;   ADDITIONAL PARENT FUNCTION BC_ISLE
LOAD:0000000000BEE288 ;   ADDITIONAL PARENT FUNCTION BC_ISGT
LOAD:0000000000BEE288
LOAD:0000000000BEE288 loc_BEE288                              ; CODE XREF: BC_ISLT+4C↑j
LOAD:0000000000BEE288                                         ; BC_ISLT+5C↑j ...
LOAD:0000000000BEE288                 ADD             X1, X19, X27,LSL#3
LOAD:0000000000BEE28C                 SUB             X21, X21, #4
LOAD:0000000000BEE290                 ADD             X2, X19, X28,LSL#3
LOAD:0000000000BEE294                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE298                 MOV             X0, X23
LOAD:0000000000BEE29C                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEE2A0                 UXTB            W3, W16
LOAD:0000000000BEE2A4                 BL              sub_BF4B60
LOAD:0000000000BEE2A8
LOAD:0000000000BEE2A8 loc_BEE2A8                              ; CODE XREF: BC_ISEQV+2518↓j
LOAD:0000000000BEE2A8                                         ; BC_ISEQV+2534↓j
LOAD:0000000000BEE2A8                 CMP             X0, #1
LOAD:0000000000BEE2AC                 B.HI            loc_BEE3B0
LOAD:0000000000BEE2B0
LOAD:0000000000BEE2B0 loc_BEE2B0                              ; CODE XREF: sub_BEE2F0+C↓j
LOAD:0000000000BEE2B0                                         ; sub_BEE300+C↓j
LOAD:0000000000BEE2B0                 LDRH            W17, [X21,#2]
LOAD:0000000000BEE2B4                 ADD             X21, X21, #4
LOAD:0000000000BEE2B8                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEE2BC                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEE2C0                 CSEL            X21, X21, X17, CC
LOAD:0000000000BEE2C4
LOAD:0000000000BEE2C4 loc_BEE2C4                              ; CODE XREF: sub_BEE0EC+34↑j
LOAD:0000000000BEE2C4                                         ; sub_BEE2DC+10↓j ...
LOAD:0000000000BEE2C4                 LDR             W16, [X21],#4
LOAD:0000000000BEE2C8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEE2CC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEE2D0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEE2D4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEE2D8                 BR              X8
LOAD:0000000000BEE2D8 ; END OF FUNCTION CHUNK FOR BC_ISLT
LOAD:0000000000BEE2DC
LOAD:0000000000BEE2DC ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEE2DC
LOAD:0000000000BEE2DC
LOAD:0000000000BEE2DC sub_BEE2DC                              ; DATA XREF: sub_BF3C38:loc_BF3D8C↓o
LOAD:0000000000BEE2DC                                         ; sub_BF4080:loc_BF4284↓o ...
LOAD:0000000000BEE2DC                 LDUR            W16, [X21,#-4]
LOAD:0000000000BEE2E0                 LDR             X8, [X27]
LOAD:0000000000BEE2E4                 UBFX            X9, X16, #8, #8
LOAD:0000000000BEE2E8                 STR             X8, [X19,X9,LSL#3]
LOAD:0000000000BEE2EC                 B               loc_BEE2C4
LOAD:0000000000BEE2EC ; End of function sub_BEE2DC
LOAD:0000000000BEE2EC
LOAD:0000000000BEE2F0
LOAD:0000000000BEE2F0 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEE2F0
LOAD:0000000000BEE2F0
LOAD:0000000000BEE2F0 sub_BEE2F0                              ; DATA XREF: sub_BF4878+F0↓o
LOAD:0000000000BEE2F0                                         ; sub_BF4878+F8↓o ...
LOAD:0000000000BEE2F0                 LDR             X8, [X27]
LOAD:0000000000BEE2F4                 MOV             X9, #0xFFFEFFFFFFFFFFFF
LOAD:0000000000BEE2F8                 CMP             X9, X8
LOAD:0000000000BEE2FC                 B               loc_BEE2B0
LOAD:0000000000BEE2FC ; End of function sub_BEE2F0
LOAD:0000000000BEE2FC
LOAD:0000000000BEE300
LOAD:0000000000BEE300 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEE300
LOAD:0000000000BEE300
LOAD:0000000000BEE300 sub_BEE300                              ; DATA XREF: sub_BF4878:loc_BF4964↓o
LOAD:0000000000BEE300                                         ; sub_BF4878+F4↓o ...
LOAD:0000000000BEE300                 LDR             X8, [X27]
LOAD:0000000000BEE304                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEE308                 CMP             X8, X9
LOAD:0000000000BEE30C                 B               loc_BEE2B0
LOAD:0000000000BEE30C ; End of function sub_BEE300
LOAD:0000000000BEE30C
LOAD:0000000000BEE310 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE310 ; START OF FUNCTION CHUNK FOR BC_ISEQV
LOAD:0000000000BEE310
LOAD:0000000000BEE310 loc_BEE310                              ; CODE XREF: BC_ISEQV+80↑j
LOAD:0000000000BEE310                                         ; BC_ISEQV+100↑j
LOAD:0000000000BEE310                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEE314                 SUB             X21, X21, #4
LOAD:0000000000BEE318                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE31C                 MOV             X0, X23
LOAD:0000000000BEE320                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEE324                 BL              sub_BF4878
LOAD:0000000000BEE328                 B               loc_BEE2A8
LOAD:0000000000BEE32C ; ---------------------------------------------------------------------------
LOAD:0000000000BEE32C
LOAD:0000000000BEE32C loc_BEE32C                              ; CODE XREF: BC_ISEQV+34↑j
LOAD:0000000000BEE32C                                         ; BC_ISEQV+B8↑j ...
LOAD:0000000000BEE32C                 SUB             X21, X21, #4
LOAD:0000000000BEE330                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE334                 MOV             X0, X23
LOAD:0000000000BEE338                 MOV             X1, X16
LOAD:0000000000BEE33C                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEE340                 BL              sub_BF49AC
LOAD:0000000000BEE344                 B               loc_BEE2A8
LOAD:0000000000BEE344 ; END OF FUNCTION CHUNK FOR BC_ISEQV
LOAD:0000000000BEE348 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE348
LOAD:0000000000BEE348 loc_BEE348                              ; CODE XREF: LOAD:0000000000BEC24C↑j
LOAD:0000000000BEE348                                         ; LOAD:0000000000BEC270↑j
LOAD:0000000000BEE348                 SUB             X21, X21, #4
LOAD:0000000000BEE34C                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE350                 MOV             X0, X23
LOAD:0000000000BEE354                 MOV             X1, X27
LOAD:0000000000BEE358                 MOV             X2, X28
LOAD:0000000000BEE35C                 STR             X21, [SP,#8]
LOAD:0000000000BEE360                 BL              sub_BF4E90
LOAD:0000000000BEE364                 B               loc_BEE2C4
LOAD:0000000000BEE368 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE368
LOAD:0000000000BEE368 loc_BEE368                              ; CODE XREF: LOAD:0000000000BEC38C↑j
LOAD:0000000000BEE368                                         ; LOAD:0000000000BEC3BC↑j ...
LOAD:0000000000BEE368                 ADD             X2, X19, X17,LSL#3
LOAD:0000000000BEE36C                 ADD             X3, X20, X28,LSL#3
LOAD:0000000000BEE370                 B               loc_BEE394
LOAD:0000000000BEE374 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE374
LOAD:0000000000BEE374 loc_BEE374                              ; CODE XREF: LOAD:0000000000BEC594↑j
LOAD:0000000000BEE374                                         ; LOAD:0000000000BEC5C4↑j ...
LOAD:0000000000BEE374                 ADD             X3, X19, X17,LSL#3
LOAD:0000000000BEE378                 ADD             X2, X20, X28,LSL#3
LOAD:0000000000BEE37C                 B               loc_BEE394
LOAD:0000000000BEE380 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE380
LOAD:0000000000BEE380 loc_BEE380                              ; CODE XREF: LOAD:0000000000BEC330↑j
LOAD:0000000000BEE380                 ADD             X2, X19, X28,LSL#3
LOAD:0000000000BEE384                 MOV             X3, X2
LOAD:0000000000BEE388                 B               loc_BEE394
LOAD:0000000000BEE38C ; ---------------------------------------------------------------------------
LOAD:0000000000BEE38C
LOAD:0000000000BEE38C loc_BEE38C                              ; CODE XREF: LOAD:0000000000BEC79C↑j
LOAD:0000000000BEE38C                                         ; LOAD:0000000000BEC7CC↑j ...
LOAD:0000000000BEE38C                 ADD             X2, X19, X17,LSL#3
LOAD:0000000000BEE390                 ADD             X3, X19, X28,LSL#3
LOAD:0000000000BEE394
LOAD:0000000000BEE394 loc_BEE394                              ; CODE XREF: LOAD:0000000000BEE370↑j
LOAD:0000000000BEE394                                         ; LOAD:0000000000BEE37C↑j ...
LOAD:0000000000BEE394                 UXTB            W4, W16
LOAD:0000000000BEE398                 ADD             X1, X19, X27,LSL#3
LOAD:0000000000BEE39C                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE3A0                 MOV             X0, X23
LOAD:0000000000BEE3A4                 STR             X21, [SP,#8]
LOAD:0000000000BEE3A8                 BL              sub_BF4080
LOAD:0000000000BEE3AC                 CBZ             X0, loc_BEE2C4
LOAD:0000000000BEE3B0 ; START OF FUNCTION CHUNK FOR BC_ISLT
LOAD:0000000000BEE3B0 ;   ADDITIONAL PARENT FUNCTION sub_BEE0EC
LOAD:0000000000BEE3B0
LOAD:0000000000BEE3B0 loc_BEE3B0                              ; CODE XREF: sub_BEE0EC-16FC↑j
LOAD:0000000000BEE3B0                                         ; BC_ISLT+269C↑j ...
LOAD:0000000000BEE3B0                 SUB             X9, X0, X19
LOAD:0000000000BEE3B4                 STUR            X21, [X0,#-0x18]
LOAD:0000000000BEE3B8                 ADD             X21, X9, #2
LOAD:0000000000BEE3BC                 MOV             X19, X0
LOAD:0000000000BEE3C0                 MOV             W28, #0x10
LOAD:0000000000BEE3C4                 B               loc_BEDFF8
LOAD:0000000000BEE3C4 ; END OF FUNCTION CHUNK FOR BC_ISLT
LOAD:0000000000BEE3C8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE3C8
LOAD:0000000000BEE3C8 loc_BEE3C8                              ; CODE XREF: LOAD:0000000000BEC318↑j
LOAD:0000000000BEE3C8                 ADD             X1, X19, X28,LSL#3
LOAD:0000000000BEE3CC                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE3D0                 MOV             X0, X23
LOAD:0000000000BEE3D4                 STR             X21, [SP,#8]
LOAD:0000000000BEE3D8                 BL              sub_BF4774
LOAD:0000000000BEE3DC                 B               loc_BEE3B0
LOAD:0000000000BEE3E0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE3E0 ; START OF FUNCTION CHUNK FOR sub_BEDF78
LOAD:0000000000BEE3E0 ;   ADDITIONAL PARENT FUNCTION BC_CALLM
LOAD:0000000000BEE3E0 ;   ADDITIONAL PARENT FUNCTION BC_ITERC
LOAD:0000000000BEE3E0
LOAD:0000000000BEE3E0 loc_BEE3E0                              ; CODE XREF: BC_CALLM+34↑j
LOAD:0000000000BEE3E0                                         ; BC_ITERC+2C↑j ...
LOAD:0000000000BEE3E0                 MOV             X0, X23
LOAD:0000000000BEE3E4                 STR             X17, [X23,#0x20]
LOAD:0000000000BEE3E8                 SUB             X1, X19, #0x10
LOAD:0000000000BEE3EC                 STR             X21, [SP,#0xC0+var_B8]
LOAD:0000000000BEE3F0                 ADD             X2, X19, X28
LOAD:0000000000BEE3F4                 BL              sub_BF4EF8
LOAD:0000000000BEE3F8                 LDUR            X2, [X19,#-0x10]
LOAD:0000000000BEE3FC                 ADD             X28, X28, #8
LOAD:0000000000BEE400                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEE404                 STUR            X21, [X19,#-8]
LOAD:0000000000BEE408                 LDR             X21, [X2,#0x20]
LOAD:0000000000BEE40C                 LDR             W16, [X21],#4
LOAD:0000000000BEE410                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEE414                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEE418                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEE41C                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BEE420                 BR              X8
LOAD:0000000000BEE420 ; END OF FUNCTION CHUNK FOR sub_BEDF78
LOAD:0000000000BEE424 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE424 ; START OF FUNCTION CHUNK FOR BC_CALLMT
LOAD:0000000000BEE424
LOAD:0000000000BEE424 loc_BEE424                              ; CODE XREF: BC_CALLMT+2C↑j
LOAD:0000000000BEE424                 MOV             X0, X23
LOAD:0000000000BEE428                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE42C                 SUB             X1, X27, #0x10
LOAD:0000000000BEE430                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEE434                 ADD             X2, X27, X28
LOAD:0000000000BEE438                 BL              sub_BF4EF8
LOAD:0000000000BEE43C                 LDUR            X9, [X27,#-0x10]
LOAD:0000000000BEE440                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEE444                 ADD             X28, X28, #8
LOAD:0000000000BEE448                 AND             X2, X9, #0x7FFFFFFFFFFF
LOAD:0000000000BEE44C                 B               BC_CALLT2_Z
LOAD:0000000000BEE44C ; END OF FUNCTION CHUNK FOR BC_CALLMT
LOAD:0000000000BEE450 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE450 ; START OF FUNCTION CHUNK FOR BC_FORI
LOAD:0000000000BEE450 ;   ADDITIONAL PARENT FUNCTION BC_FORL
LOAD:0000000000BEE450 ;   ADDITIONAL PARENT FUNCTION BC_IFORL
LOAD:0000000000BEE450
LOAD:0000000000BEE450 loc_BEE450                              ; CODE XREF: BC_FORI+20↑j
LOAD:0000000000BEE450                                         ; BC_FORI+28↑j ...
LOAD:0000000000BEE450                 MOV             X0, X23
LOAD:0000000000BEE454                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE458                 MOV             X1, X27
LOAD:0000000000BEE45C                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEE460                 BL              sub_BF4FD4
LOAD:0000000000BEE464                 LDUR            W16, [X21,#-4]
LOAD:0000000000BEE468                 UXTB            W8, W16
LOAD:0000000000BEE46C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEE470                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEE474                 CMP             X8, #0x5E ; '^'
LOAD:0000000000BEE478                 B.EQ            BC_JFORL
LOAD:0000000000BEE47C                 B               BC_FORI
LOAD:0000000000BEE47C ; END OF FUNCTION CHUNK FOR BC_FORI
LOAD:0000000000BEE480 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE480                 LDR             X0, [X19]
LOAD:0000000000BEE484                 CMP             X28, #8
LOAD:0000000000BEE488                 B.CC            loc_BEF3BC
LOAD:0000000000BEE48C                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEE490                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEE494                 CMP             X0, X9
LOAD:0000000000BEE498                 B.CS            loc_BEF3BC
LOAD:0000000000BEE49C                 STUR            X0, [X19,#-0x10]
LOAD:0000000000BEE4A0                 SUB             X17, X19, #8
LOAD:0000000000BEE4A4                 SUBS            X27, X28, #8
LOAD:0000000000BEE4A8                 ADD             X28, X28, #8
LOAD:0000000000BEE4AC                 CBZ             X27, loc_BEEB00
LOAD:0000000000BEE4B0
LOAD:0000000000BEE4B0 loc_BEE4B0                              ; CODE XREF: LOAD:0000000000BEE4BC↓j
LOAD:0000000000BEE4B0                 LDR             X0, [X17,#0x10]
LOAD:0000000000BEE4B4                 SUB             X27, X27, #8
LOAD:0000000000BEE4B8                 STR             X0, [X17],#8
LOAD:0000000000BEE4BC                 CBNZ            X27, loc_BEE4B0
LOAD:0000000000BEE4C0                 B               loc_BEEB00
LOAD:0000000000BEE4C4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE4C4                 LDR             X0, [X19]
LOAD:0000000000BEE4C8                 CMP             X28, #8
LOAD:0000000000BEE4CC                 B.CC            loc_BEF3BC
LOAD:0000000000BEE4D0                 MOV             W8, #0xD
LOAD:0000000000BEE4D4                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEE4D8                 CMN             X15, #0xD
LOAD:0000000000BEE4DC                 CSINV           X9, X8, X15, CC
LOAD:0000000000BEE4E0                 ADD             X9, X9, #6
LOAD:0000000000BEE4E4                 LDR             X0, [X2,X9,LSL#3]
LOAD:0000000000BEE4E8                 B               loc_BEEAF4
LOAD:0000000000BEE4EC ; ---------------------------------------------------------------------------
LOAD:0000000000BEE4EC                 LDR             X0, [X19]
LOAD:0000000000BEE4F0                 CMP             X28, #8
LOAD:0000000000BEE4F4                 B.CC            loc_BEF3BC
LOAD:0000000000BEE4F8                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEE4FC                 CMN             X15, #0xC
LOAD:0000000000BEE500                 CCMN            X15, #0xD, #4, NE
LOAD:0000000000BEE504                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEE508                 B.NE            loc_BEE568
LOAD:0000000000BEE50C                 LDR             X17, [X0,#0x20]
LOAD:0000000000BEE510
LOAD:0000000000BEE510 loc_BEE510                              ; CODE XREF: LOAD:0000000000BEE57C↓j
LOAD:0000000000BEE510                 MOV             X0, X26
LOAD:0000000000BEE514                 LDR             X28, [X22,#0x230]
LOAD:0000000000BEE518                 CBZ             X17, loc_BEEAF4
LOAD:0000000000BEE51C                 LDR             W9, [X17,#0x34]
LOAD:0000000000BEE520                 LDR             W10, [X28,#0xC]
LOAD:0000000000BEE524                 LDR             X2, [X17,#0x28]
LOAD:0000000000BEE528                 AND             W9, W9, W10
LOAD:0000000000BEE52C                 ADD             X9, X9, X9,LSL#1
LOAD:0000000000BEE530                 MOV             X3, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEE534                 ADD             X2, X2, X9,LSL#3
LOAD:0000000000BEE538                 ADD             X3, X28, X3,LSL#47
LOAD:0000000000BEE53C
LOAD:0000000000BEE53C loc_BEE53C                              ; CODE XREF: LOAD:0000000000BEE54C↓j
LOAD:0000000000BEE53C                 LDP             X0, X8, [X2]
LOAD:0000000000BEE540                 LDR             X2, [X2,#0x10]
LOAD:0000000000BEE544                 CMP             X8, X3
LOAD:0000000000BEE548                 B.EQ            loc_BEE55C
LOAD:0000000000BEE54C                 CBNZ            X2, loc_BEE53C
LOAD:0000000000BEE550
LOAD:0000000000BEE550 loc_BEE550                              ; CODE XREF: LOAD:0000000000BEE564↓j
LOAD:0000000000BEE550                 MOV             X0, X17
LOAD:0000000000BEE554                 MOVK            X0, #0xFFFA,LSL#48
LOAD:0000000000BEE558                 B               loc_BEEAF4
LOAD:0000000000BEE55C ; ---------------------------------------------------------------------------
LOAD:0000000000BEE55C
LOAD:0000000000BEE55C loc_BEE55C                              ; CODE XREF: LOAD:0000000000BEE548↑j
LOAD:0000000000BEE55C                 CMP             X8, X26
LOAD:0000000000BEE560                 B.NE            loc_BEEAF4
LOAD:0000000000BEE564                 B               loc_BEE550
LOAD:0000000000BEE568 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE568
LOAD:0000000000BEE568 loc_BEE568                              ; CODE XREF: LOAD:0000000000BEE508↑j
LOAD:0000000000BEE568                 MOV             X8, #0xFFFFFFFFFFFFFFF2
LOAD:0000000000BEE56C                 CMP             X15, X8
LOAD:0000000000BEE570                 CSEL            X15, X15, X8, CS
LOAD:0000000000BEE574                 SUB             X9, X22, X15,LSL#3
LOAD:0000000000BEE578                 LDR             X17, [X9,#0x250]
LOAD:0000000000BEE57C                 B               loc_BEE510
LOAD:0000000000BEE580 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE580                 LDP             X0, X1, [X19]
LOAD:0000000000BEE584                 CMP             X28, #0x10
LOAD:0000000000BEE588                 B.CC            loc_BEF3BC
LOAD:0000000000BEE58C                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEE590                 CMN             X15, #0xC
LOAD:0000000000BEE594                 AND             X9, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEE598                 B.NE            loc_BEF3BC
LOAD:0000000000BEE59C                 LDR             X8, [X9,#0x20]
LOAD:0000000000BEE5A0                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BEE5A4                 LDRB            W10, [X9,#8]
LOAD:0000000000BEE5A8                 CMN             X15, #0xC
LOAD:0000000000BEE5AC                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BEE5B0                 CCMP            X8, #0, #0, EQ
LOAD:0000000000BEE5B4                 B.NE            loc_BEF3BC
LOAD:0000000000BEE5B8                 STR             X1, [X9,#0x20]
LOAD:0000000000BEE5BC                 TBZ             W10, #2, loc_BEEAF4
LOAD:0000000000BEE5C0                 LDR             X8, [X22,#0x40]
LOAD:0000000000BEE5C4                 AND             W10, W10, #0xFFFFFFFB
LOAD:0000000000BEE5C8                 STR             X9, [X22,#0x40]
LOAD:0000000000BEE5CC                 STRB            W10, [X9,#8]
LOAD:0000000000BEE5D0                 STR             X8, [X9,#0x18]
LOAD:0000000000BEE5D4                 B               loc_BEEAF4
LOAD:0000000000BEE5D8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE5D8                 LDR             X1, [X19]
LOAD:0000000000BEE5DC                 CMP             X28, #0x10
LOAD:0000000000BEE5E0                 B.CC            loc_BEF3BC
LOAD:0000000000BEE5E4                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BEE5E8                 CMN             X15, #0xC
LOAD:0000000000BEE5EC                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BEE5F0                 B.NE            loc_BEF3BC
LOAD:0000000000BEE5F4                 MOV             X0, X23
LOAD:0000000000BEE5F8                 ADD             X2, X19, #8
LOAD:0000000000BEE5FC                 BL              sub_BF2EE0
LOAD:0000000000BEE600                 LDR             X0, [X0]
LOAD:0000000000BEE604                 B               loc_BEEAF4
LOAD:0000000000BEE608 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE608                 LDR             X0, [X19]
LOAD:0000000000BEE60C                 CMP             X28, #8
LOAD:0000000000BEE610                 B.NE            loc_BEF3BC
LOAD:0000000000BEE614                 CMP             X25, X0,LSR#32
LOAD:0000000000BEE618                 B.CC            loc_BEF3BC
LOAD:0000000000BEE61C                 B               loc_BEEAF4
LOAD:0000000000BEE620 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE620                 LDR             X0, [X19]
LOAD:0000000000BEE624                 CMP             X28, #8
LOAD:0000000000BEE628                 B.CC            loc_BEF3BC
LOAD:0000000000BEE62C                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEE630                 CMN             X15, #5
LOAD:0000000000BEE634                 B.EQ            loc_BEEAF4
LOAD:0000000000BEE638                 LDR             X9, [X22,#0x2C0]
LOAD:0000000000BEE63C                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE640                 CMN             X15, #0xE
LOAD:0000000000BEE644                 CCMP            X9, #0, #0, LS
LOAD:0000000000BEE648                 STR             X21, [SP,#8]
LOAD:0000000000BEE64C                 B.NE            loc_BEF3BC
LOAD:0000000000BEE650                 LDP             X0, X1, [X22,#0x10]
LOAD:0000000000BEE654                 CMP             X0, X1
LOAD:0000000000BEE658                 B.LT            loc_BEE660
LOAD:0000000000BEE65C                 BL              sub_BEF464
LOAD:0000000000BEE660
LOAD:0000000000BEE660 loc_BEE660                              ; CODE XREF: LOAD:0000000000BEE658↑j
LOAD:0000000000BEE660                 MOV             X0, X23
LOAD:0000000000BEE664                 MOV             X1, X19
LOAD:0000000000BEE668                 BL              sub_BCFB50
LOAD:0000000000BEE66C                 MOV             X9, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEE670                 LDR             X19, [X23,#0x20]
LOAD:0000000000BEE674                 ADD             X0, X0, X9,LSL#47
LOAD:0000000000BEE678                 B               loc_BEEAF4
LOAD:0000000000BEE67C ; ---------------------------------------------------------------------------
LOAD:0000000000BEE67C                 LDR             X0, [X19]
LOAD:0000000000BEE680                 CMP             X28, #8
LOAD:0000000000BEE684                 B.CC            loc_BEF3BC
LOAD:0000000000BEE688                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEE68C                 CMN             X15, #0xC
LOAD:0000000000BEE690                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEE694                 B.NE            loc_BEF3BC
LOAD:0000000000BEE698                 STR             X26, [X19,X28]
LOAD:0000000000BEE69C                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEE6A0                 ADD             X1, X19, #8
LOAD:0000000000BEE6A4                 SUB             X2, X19, #0x10
LOAD:0000000000BEE6A8                 BL              sub_BF3508
LOAD:0000000000BEE6AC                 MOV             W28, #0x18
LOAD:0000000000BEE6B0                 TBNZ            W0, #0x1F, loc_BEF3BC
LOAD:0000000000BEE6B4                 CBNZ            X0, loc_BEEB00
LOAD:0000000000BEE6B8                 STUR            X26, [X19,#-0x10]
LOAD:0000000000BEE6BC                 B               loc_BEEAFC
LOAD:0000000000BEE6C0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE6C0                 LDR             X0, [X19]
LOAD:0000000000BEE6C4                 CMP             X28, #8
LOAD:0000000000BEE6C8                 B.CC            loc_BEF3BC
LOAD:0000000000BEE6CC                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEE6D0                 CMN             X15, #0xC
LOAD:0000000000BEE6D4                 AND             X9, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEE6D8                 B.NE            loc_BEF3BC
LOAD:0000000000BEE6DC                 LDR             X3, [X2,#0x30]
LOAD:0000000000BEE6E0                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEE6E4                 MOV             W28, #0x20 ; ' '
LOAD:0000000000BEE6E8                 STP             X0, X26, [X19,#-8]
LOAD:0000000000BEE6EC                 STUR            X3, [X19,#-0x10]
LOAD:0000000000BEE6F0                 B               loc_BEEB00
LOAD:0000000000BEE6F4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE6F4                 LDP             X0, X1, [X19]
LOAD:0000000000BEE6F8                 CMP             X28, #0x10
LOAD:0000000000BEE6FC                 B.CC            loc_BEF3BC
LOAD:0000000000BEE700                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEE704                 CMN             X15, #0xC
LOAD:0000000000BEE708                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEE70C                 B.NE            loc_BEF3BC
LOAD:0000000000BEE710                 CMP             X25, X1,LSR#32
LOAD:0000000000BEE714                 B.NE            loc_BEF3BC
LOAD:0000000000BEE718                 LDR             W9, [X0,#0x30]
LOAD:0000000000BEE71C                 LDR             X2, [X0,#0x10]
LOAD:0000000000BEE720                 LDR             W8, [X0,#0x34]
LOAD:0000000000BEE724                 ADD             W1, W1, #1
LOAD:0000000000BEE728                 CMP             W1, W9
LOAD:0000000000BEE72C                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEE730                 ADD             X10, X1, X24
LOAD:0000000000BEE734                 MOV             W28, #8
LOAD:0000000000BEE738                 STUR            X10, [X19,#-0x10]
LOAD:0000000000BEE73C                 B.CS            loc_BEE758
LOAD:0000000000BEE740                 LDR             X8, [X2,X1,LSL#3]
LOAD:0000000000BEE744
LOAD:0000000000BEE744 loc_BEE744                              ; CODE XREF: LOAD:0000000000BEE768↓j
LOAD:0000000000BEE744                 MOV             W9, #0x18
LOAD:0000000000BEE748                 CMP             X8, X26
LOAD:0000000000BEE74C                 STUR            X8, [X19,#-8]
LOAD:0000000000BEE750                 CSEL            X28, X28, X9, EQ
LOAD:0000000000BEE754                 B               loc_BEEB00
LOAD:0000000000BEE758 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE758
LOAD:0000000000BEE758 loc_BEE758                              ; CODE XREF: LOAD:0000000000BEE73C↑j
LOAD:0000000000BEE758                 CBZ             W8, loc_BEEB00
LOAD:0000000000BEE75C                 BL              sub_BF2E24
LOAD:0000000000BEE760                 CBZ             X0, loc_BEEB00
LOAD:0000000000BEE764                 LDR             X8, [X0]
LOAD:0000000000BEE768                 B               loc_BEE744
LOAD:0000000000BEE76C ; ---------------------------------------------------------------------------
LOAD:0000000000BEE76C                 LDR             X0, [X19]
LOAD:0000000000BEE770                 CMP             X28, #8
LOAD:0000000000BEE774                 B.CC            loc_BEF3BC
LOAD:0000000000BEE778                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEE77C                 CMN             X15, #0xC
LOAD:0000000000BEE780                 AND             X9, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEE784                 B.NE            loc_BEF3BC
LOAD:0000000000BEE788                 LDR             X3, [X2,#0x30]
LOAD:0000000000BEE78C                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEE790                 MOV             W28, #0x20 ; ' '
LOAD:0000000000BEE794                 STP             X0, X24, [X19,#-8]
LOAD:0000000000BEE798                 STUR            X3, [X19,#-0x10]
LOAD:0000000000BEE79C                 B               loc_BEEB00
LOAD:0000000000BEE7A0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE7A0                 CMP             X28, #8
LOAD:0000000000BEE7A4                 LDRB            W8, [X22,#0x91]
LOAD:0000000000BEE7A8                 B.CC            loc_BEF3BC
LOAD:0000000000BEE7AC                 SUB             X28, X28, #8
LOAD:0000000000BEE7B0                 MOV             X17, X19
LOAD:0000000000BEE7B4                 ADD             X19, X19, #0x10
LOAD:0000000000BEE7B8                 UBFX            W8, W8, #4, #1
LOAD:0000000000BEE7BC                 ADD             X21, X8, #0x16
LOAD:0000000000BEE7C0                 B.EQ            loc_BEDFF8
LOAD:0000000000BEE7C4
LOAD:0000000000BEE7C4 loc_BEE7C4                              ; CODE XREF: LOAD:0000000000BEE814↓j
LOAD:0000000000BEE7C4                 ADD             X10, X19, X28
LOAD:0000000000BEE7C8
LOAD:0000000000BEE7C8 loc_BEE7C8                              ; CODE XREF: LOAD:0000000000BEE7D4↓j
LOAD:0000000000BEE7C8                 LDUR            X8, [X10,#-0x10]
LOAD:0000000000BEE7CC                 STR             X8, [X10,#-8]!
LOAD:0000000000BEE7D0                 CMP             X10, X19
LOAD:0000000000BEE7D4                 B.NE            loc_BEE7C8
LOAD:0000000000BEE7D8                 B               loc_BEDFF8
LOAD:0000000000BEE7DC ; ---------------------------------------------------------------------------
LOAD:0000000000BEE7DC                 LDP             X0, X1, [X19]
LOAD:0000000000BEE7E0                 LDRB            W8, [X22,#0x91]
LOAD:0000000000BEE7E4                 SUBS            X9, X28, #0x10
LOAD:0000000000BEE7E8                 B.CC            loc_BEF3BC
LOAD:0000000000BEE7EC                 MOV             X17, X19
LOAD:0000000000BEE7F0                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BEE7F4                 UBFX            W8, W8, #4, #1
LOAD:0000000000BEE7F8                 CMN             X15, #9
LOAD:0000000000BEE7FC                 ADD             X21, X8, #0x1E
LOAD:0000000000BEE800                 B.NE            loc_BEF3BC
LOAD:0000000000BEE804                 MOV             X28, X9
LOAD:0000000000BEE808                 ADD             X19, X19, #0x18
LOAD:0000000000BEE80C                 STP             X1, X0, [X17]
LOAD:0000000000BEE810                 CBZ             X28, loc_BEDFF8
LOAD:0000000000BEE814                 B               loc_BEE7C4
LOAD:0000000000BEE818 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE818                 LDR             X0, [X19]
LOAD:0000000000BEE81C                 CMP             X28, #8
LOAD:0000000000BEE820                 B.CC            loc_BEF3BC
LOAD:0000000000BEE824                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEE828                 CMN             X15, #7
LOAD:0000000000BEE82C                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEE830                 B.NE            loc_BEF3BC
LOAD:0000000000BEE834                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEE838                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE83C                 LDP             X17, X1, [X0,#0x20]
LOAD:0000000000BEE840                 LDRB            W9, [X0,#0xB]
LOAD:0000000000BEE844                 ADD             X8, X1, X9
LOAD:0000000000BEE848                 STR             X21, [SP,#8]
LOAD:0000000000BEE84C                 CMP             X8, X17
LOAD:0000000000BEE850                 B.EQ            loc_BEF3BC
LOAD:0000000000BEE854                 CMP             X9, #1
LOAD:0000000000BEE858                 ADD             X8, X1, #8
LOAD:0000000000BEE85C                 CSEL            X1, X1, X8, CS
LOAD:0000000000BEE860                 LDR             X3, [X0,#0x30]
LOAD:0000000000BEE864                 ADD             X2, X1, X28
LOAD:0000000000BEE868                 LDR             X17, [X0,#0x50]
LOAD:0000000000BEE86C                 CCMP            X2, X3, #2, LS
LOAD:0000000000BEE870                 CCMP            X17, #0, #2, LS
LOAD:0000000000BEE874                 B.HI            loc_BEF3BC
LOAD:0000000000BEE878                 SUB             X2, X2, #8
LOAD:0000000000BEE87C                 ADD             X19, X19, #8
LOAD:0000000000BEE880                 SUB             X28, X28, #8
LOAD:0000000000BEE884                 STR             X2, [X0,#0x28]
LOAD:0000000000BEE888                 STR             X19, [X23,#0x28]
LOAD:0000000000BEE88C                 CBZ             X28, loc_BEE8A4
LOAD:0000000000BEE890
LOAD:0000000000BEE890 loc_BEE890                              ; CODE XREF: LOAD:0000000000BEE8A0↓j
LOAD:0000000000BEE890                 LDR             X8, [X19,X17]
LOAD:0000000000BEE894                 CMP             X17, X28
LOAD:0000000000BEE898                 STR             X8, [X1,X17]
LOAD:0000000000BEE89C                 ADD             X17, X17, #8
LOAD:0000000000BEE8A0                 B.NE            loc_BEE890
LOAD:0000000000BEE8A4
LOAD:0000000000BEE8A4 loc_BEE8A4                              ; CODE XREF: LOAD:0000000000BEE88C↑j
LOAD:0000000000BEE8A4                 MOV             W2, #0
LOAD:0000000000BEE8A8                 MOV             X27, X0
LOAD:0000000000BEE8AC                 MOV             W3, #0
LOAD:0000000000BEE8B0                 BL              sub_BEDE9C
LOAD:0000000000BEE8B4
LOAD:0000000000BEE8B4 loc_BEE8B4                              ; CODE XREF: LOAD:0000000000BEE950↓j
LOAD:0000000000BEE8B4                 LDP             X2, X3, [X27,#0x20]
LOAD:0000000000BEE8B8                 CMP             X0, #1
LOAD:0000000000BEE8BC                 LDR             X19, [X23,#0x20]
LOAD:0000000000BEE8C0                 STR             X23, [X22,#0x170]
LOAD:0000000000BEE8C4                 STR             W26, [X22,#0xB8]
LOAD:0000000000BEE8C8                 B.HI            loc_BEE928
LOAD:0000000000BEE8CC                 SUB             X28, X3, X2
LOAD:0000000000BEE8D0                 LDR             X0, [X23,#0x30]
LOAD:0000000000BEE8D4                 ADD             X1, X19, X28
LOAD:0000000000BEE8D8                 CBZ             X28, loc_BEE904
LOAD:0000000000BEE8DC                 CMP             X1, X0
LOAD:0000000000BEE8E0                 MOV             W17, #0
LOAD:0000000000BEE8E4                 B.HI            loc_BEE940
LOAD:0000000000BEE8E8                 SUB             X3, X28, #8
LOAD:0000000000BEE8EC                 STR             X2, [X27,#0x28]
LOAD:0000000000BEE8F0
LOAD:0000000000BEE8F0 loc_BEE8F0                              ; CODE XREF: LOAD:0000000000BEE900↓j
LOAD:0000000000BEE8F0                 LDR             X8, [X2,X17]
LOAD:0000000000BEE8F4                 CMP             X17, X3
LOAD:0000000000BEE8F8                 STR             X8, [X19,X17]
LOAD:0000000000BEE8FC                 ADD             X17, X17, #8
LOAD:0000000000BEE900                 B.NE            loc_BEE8F0
LOAD:0000000000BEE904
LOAD:0000000000BEE904 loc_BEE904                              ; CODE XREF: LOAD:0000000000BEE8D8↑j
LOAD:0000000000BEE904                 MOV             X9, #0xFFFEFFFFFFFFFFFF
LOAD:0000000000BEE908                 ADD             X28, X28, #0x10
LOAD:0000000000BEE90C
LOAD:0000000000BEE90C loc_BEE90C                              ; CODE XREF: LOAD:0000000000BEE93C↓j
LOAD:0000000000BEE90C                 STUR            X9, [X19,#-8]
LOAD:0000000000BEE910                 SUB             X27, X19, #8
LOAD:0000000000BEE914                 ANDS            X0, X21, #3
LOAD:0000000000BEE918                 STR             X21, [SP,#8]
LOAD:0000000000BEE91C                 STR             W28, [SP,#0x20]
LOAD:0000000000BEE920                 B.EQ            loc_BED6A4
LOAD:0000000000BEE924                 B               loc_BEDD2C
LOAD:0000000000BEE928 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE928
LOAD:0000000000BEE928 loc_BEE928                              ; CODE XREF: LOAD:0000000000BEE8C8↑j
LOAD:0000000000BEE928                 LDR             X8, [X3,#-8]!
LOAD:0000000000BEE92C                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEE930                 MOV             W28, #0x18
LOAD:0000000000BEE934                 STR             X3, [X27,#0x28]
LOAD:0000000000BEE938                 STR             X8, [X19]
LOAD:0000000000BEE93C                 B               loc_BEE90C
LOAD:0000000000BEE940 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE940
LOAD:0000000000BEE940 loc_BEE940                              ; CODE XREF: LOAD:0000000000BEE8E4↑j
LOAD:0000000000BEE940                 MOV             X0, X23
LOAD:0000000000BEE944                 LSR             X1, X28, #3
LOAD:0000000000BEE948                 BL              sub_BCCCCC
LOAD:0000000000BEE94C                 MOV             W0, #0
LOAD:0000000000BEE950                 B               loc_BEE8B4
LOAD:0000000000BEE954 ; ---------------------------------------------------------------------------
LOAD:0000000000BEE954                 LDR             X0, [X2,#0x30]
LOAD:0000000000BEE958                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEE95C                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEE960                 STR             X19, [X23,#0x20]
LOAD:0000000000BEE964                 LDP             X17, X1, [X0,#0x20]
LOAD:0000000000BEE968                 LDRB            W9, [X0,#0xB]
LOAD:0000000000BEE96C                 ADD             X8, X1, X9
LOAD:0000000000BEE970                 STR             X21, [SP,#8]
LOAD:0000000000BEE974                 CMP             X8, X17
LOAD:0000000000BEE978                 B.EQ            loc_BEF3BC
LOAD:0000000000BEE97C                 CMP             X9, #1
LOAD:0000000000BEE980                 ADD             X8, X1, #8
LOAD:0000000000BEE984                 CSEL            X1, X1, X8, CS
LOAD:0000000000BEE988                 LDR             X3, [X0,#0x30]
LOAD:0000000000BEE98C                 ADD             X2, X1, X28
LOAD:0000000000BEE990                 LDR             X17, [X0,#0x50]
LOAD:0000000000BEE994                 CCMP            X2, X3, #2, LS
LOAD:0000000000BEE998                 CCMP            X17, #0, #2, LS
LOAD:0000000000BEE99C                 B.HI            loc_BEF3BC
LOAD:0000000000BEE9A0                 STR             X2, [X0,#0x28]
LOAD:0000000000BEE9A4                 STR             X19, [X23,#0x28]
LOAD:0000000000BEE9A8                 CBZ             X28, loc_BEE9C0
LOAD:0000000000BEE9AC
LOAD:0000000000BEE9AC loc_BEE9AC                              ; CODE XREF: LOAD:0000000000BEE9BC↓j
LOAD:0000000000BEE9AC                 LDR             X8, [X19,X17]
LOAD:0000000000BEE9B0                 CMP             X17, X28
LOAD:0000000000BEE9B4                 STR             X8, [X1,X17]
LOAD:0000000000BEE9B8                 ADD             X17, X17, #8
LOAD:0000000000BEE9BC                 B.NE            loc_BEE9AC
LOAD:0000000000BEE9C0
LOAD:0000000000BEE9C0 loc_BEE9C0                              ; CODE XREF: LOAD:0000000000BEE9A8↑j
LOAD:0000000000BEE9C0                 MOV             W2, #0
LOAD:0000000000BEE9C4                 MOV             X27, X0
LOAD:0000000000BEE9C8                 MOV             W3, #0
LOAD:0000000000BEE9CC                 BL              sub_BEDE9C
LOAD:0000000000BEE9D0
LOAD:0000000000BEE9D0 loc_BEE9D0                              ; CODE XREF: LOAD:0000000000BEEA58↓j
LOAD:0000000000BEE9D0                 LDP             X2, X3, [X27,#0x20]
LOAD:0000000000BEE9D4                 CMP             X0, #1
LOAD:0000000000BEE9D8                 LDR             X19, [X23,#0x20]
LOAD:0000000000BEE9DC                 STR             X23, [X22,#0x170]
LOAD:0000000000BEE9E0                 STR             W26, [X22,#0xB8]
LOAD:0000000000BEE9E4                 B.HI            loc_BEEA3C
LOAD:0000000000BEE9E8                 SUB             X28, X3, X2
LOAD:0000000000BEE9EC                 LDR             X0, [X23,#0x30]
LOAD:0000000000BEE9F0                 ADD             X1, X19, X28
LOAD:0000000000BEE9F4                 CBZ             X28, loc_BEEA20
LOAD:0000000000BEE9F8                 CMP             X1, X0
LOAD:0000000000BEE9FC                 MOV             W17, #0
LOAD:0000000000BEEA00                 B.HI            loc_BEEA48
LOAD:0000000000BEEA04                 SUB             X3, X28, #8
LOAD:0000000000BEEA08                 STR             X2, [X27,#0x28]
LOAD:0000000000BEEA0C
LOAD:0000000000BEEA0C loc_BEEA0C                              ; CODE XREF: LOAD:0000000000BEEA1C↓j
LOAD:0000000000BEEA0C                 LDR             X8, [X2,X17]
LOAD:0000000000BEEA10                 CMP             X17, X3
LOAD:0000000000BEEA14                 STR             X8, [X19,X17]
LOAD:0000000000BEEA18                 ADD             X17, X17, #8
LOAD:0000000000BEEA1C                 B.NE            loc_BEEA0C
LOAD:0000000000BEEA20
LOAD:0000000000BEEA20 loc_BEEA20                              ; CODE XREF: LOAD:0000000000BEE9F4↑j
LOAD:0000000000BEEA20                 MOV             X27, X19
LOAD:0000000000BEEA24                 ADD             X28, X28, #8
LOAD:0000000000BEEA28                 ANDS            X0, X21, #3
LOAD:0000000000BEEA2C                 STR             X21, [SP,#8]
LOAD:0000000000BEEA30                 STR             W28, [SP,#0x20]
LOAD:0000000000BEEA34                 B.EQ            loc_BED6A4
LOAD:0000000000BEEA38                 B               loc_BEDD2C
LOAD:0000000000BEEA3C ; ---------------------------------------------------------------------------
LOAD:0000000000BEEA3C
LOAD:0000000000BEEA3C loc_BEEA3C                              ; CODE XREF: LOAD:0000000000BEE9E4↑j
LOAD:0000000000BEEA3C                 MOV             X0, X23
LOAD:0000000000BEEA40                 MOV             X1, X27
LOAD:0000000000BEEA44                 BL              loc_C1F460
LOAD:0000000000BEEA48
LOAD:0000000000BEEA48 loc_BEEA48                              ; CODE XREF: LOAD:0000000000BEEA00↑j
LOAD:0000000000BEEA48                 MOV             X0, X23
LOAD:0000000000BEEA4C                 LSR             X1, X28, #3
LOAD:0000000000BEEA50                 BL              sub_BCCCCC
LOAD:0000000000BEEA54                 MOV             W0, #0
LOAD:0000000000BEEA58                 B               loc_BEE9D0
LOAD:0000000000BEEA5C ; ---------------------------------------------------------------------------
LOAD:0000000000BEEA5C                 LDR             X8, [X23,#0x50]
LOAD:0000000000BEEA60                 ADD             X9, X19, X28
LOAD:0000000000BEEA64                 MOV             W0, #1
LOAD:0000000000BEEA68                 STP             X19, X9, [X23,#0x20]
LOAD:0000000000BEEA6C                 TBZ             W8, #0, loc_BEF3BC
LOAD:0000000000BEEA70                 STR             XZR, [X23,#0x50]
LOAD:0000000000BEEA74                 STRB            W0, [X23,#0xB]
LOAD:0000000000BEEA78                 B               loc_BEDD80
LOAD:0000000000BEEA7C ; ---------------------------------------------------------------------------
LOAD:0000000000BEEA7C                 LDR             X0, [X19]
LOAD:0000000000BEEA80                 CMP             X28, #8
LOAD:0000000000BEEA84                 LDR             D0, [X19]
LOAD:0000000000BEEA88                 B.CC            loc_BEF3BC
LOAD:0000000000BEEA8C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEA90                 B.EQ            loc_BEEAF4
LOAD:0000000000BEEA94                 B.CC            loc_BEF3BC
LOAD:0000000000BEEA98                 FRINTM          D0, D0
LOAD:0000000000BEEA9C                 B               loc_BEEB6C
LOAD:0000000000BEEAA0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEAA0                 LDR             X0, [X19]
LOAD:0000000000BEEAA4                 CMP             X28, #8
LOAD:0000000000BEEAA8                 LDR             D0, [X19]
LOAD:0000000000BEEAAC                 B.CC            loc_BEF3BC
LOAD:0000000000BEEAB0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEAB4                 B.EQ            loc_BEEAF4
LOAD:0000000000BEEAB8                 B.CC            loc_BEF3BC
LOAD:0000000000BEEABC                 FRINTP          D0, D0
LOAD:0000000000BEEAC0                 B               loc_BEEB6C
LOAD:0000000000BEEAC4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEAC4                 LDR             X0, [X19]
LOAD:0000000000BEEAC8                 CMP             X28, #8
LOAD:0000000000BEEACC                 B.CC            loc_BEF3BC
LOAD:0000000000BEEAD0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEAD4                 B.CC            loc_BEF3BC
LOAD:0000000000BEEAD8                 AND             X0, X0, #0x7FFFFFFFFFFFFFFF
LOAD:0000000000BEEADC                 B.NE            loc_BEEAF4
LOAD:0000000000BEEAE0                 EOR             W1, W0, W0,ASR#31
LOAD:0000000000BEEAE4                 MOV             X2, #0x41E0000000000000
LOAD:0000000000BEEAE8                 SUBS            W0, W1, W0,ASR#31
LOAD:0000000000BEEAEC                 ADD             X0, X0, X24
LOAD:0000000000BEEAF0                 CSEL            X0, X0, X2, PL
LOAD:0000000000BEEAF4 ; START OF FUNCTION CHUNK FOR sub_BEF134
LOAD:0000000000BEEAF4
LOAD:0000000000BEEAF4 loc_BEEAF4                              ; CODE XREF: LOAD:0000000000BEE4E8↑j
LOAD:0000000000BEEAF4                                         ; LOAD:0000000000BEE518↑j ...
LOAD:0000000000BEEAF4                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEEAF8                 STUR            X0, [X19,#-0x10]
LOAD:0000000000BEEAFC
LOAD:0000000000BEEAFC loc_BEEAFC                              ; CODE XREF: LOAD:0000000000BEE6BC↑j
LOAD:0000000000BEEAFC                                         ; LOAD:0000000000BEEB74↓j ...
LOAD:0000000000BEEAFC                 MOV             W28, #0x10
LOAD:0000000000BEEB00
LOAD:0000000000BEEB00 loc_BEEB00                              ; CODE XREF: LOAD:0000000000BEE4AC↑j
LOAD:0000000000BEEB00                                         ; LOAD:0000000000BEE4C0↑j ...
LOAD:0000000000BEEB00                 ANDS            X0, X21, #3
LOAD:0000000000BEEB04                 STR             W28, [SP,#arg_20]
LOAD:0000000000BEEB08                 SUB             X27, X19, #0x10
LOAD:0000000000BEEB0C                 B.NE            loc_BEDD2C
LOAD:0000000000BEEB10                 LDUR            W16, [X21,#-4]
LOAD:0000000000BEEB14                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEEB18
LOAD:0000000000BEEB18 loc_BEEB18                              ; CODE XREF: sub_BEF134-5E8↓j
LOAD:0000000000BEEB18                 CMP             X28, X17,LSL#3
LOAD:0000000000BEEB1C                 B.CC            loc_BEEB40
LOAD:0000000000BEEB20                 UBFX            X9, X16, #8, #8
LOAD:0000000000BEEB24                 SUB             X19, X27, X9,LSL#3
LOAD:0000000000BEEB28                 LDR             W16, [X21],#4
LOAD:0000000000BEEB2C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEEB30                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEEB34                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEEB38                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEEB3C                 BR              X8
LOAD:0000000000BEEB40 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEB40
LOAD:0000000000BEEB40 loc_BEEB40                              ; CODE XREF: sub_BEF134-618↑j
LOAD:0000000000BEEB40                 ADD             X9, X27, X28
LOAD:0000000000BEEB44                 ADD             X28, X28, #8
LOAD:0000000000BEEB48                 STUR            X26, [X9,#-8]
LOAD:0000000000BEEB4C                 B               loc_BEEB18
LOAD:0000000000BEEB4C ; END OF FUNCTION CHUNK FOR sub_BEF134
LOAD:0000000000BEEB50 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEB50                 LDR             X0, [X19]
LOAD:0000000000BEEB54                 CMP             X28, #8
LOAD:0000000000BEEB58                 LDR             D0, [X19]
LOAD:0000000000BEEB5C                 B.CC            loc_BEF3BC
LOAD:0000000000BEEB60                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEB64                 B.LS            loc_BEF3BC
LOAD:0000000000BEEB68                 FSQRT           D0, D0
LOAD:0000000000BEEB6C
LOAD:0000000000BEEB6C loc_BEEB6C                              ; CODE XREF: LOAD:0000000000BEEA9C↑j
LOAD:0000000000BEEB6C                                         ; LOAD:0000000000BEEAC0↑j ...
LOAD:0000000000BEEB6C                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEEB70                 STUR            D0, [X19,#-0x10]
LOAD:0000000000BEEB74                 B               loc_BEEAFC
LOAD:0000000000BEEB78 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEB78                 LDR             X0, [X19]
LOAD:0000000000BEEB7C                 CMP             X28, #8
LOAD:0000000000BEEB80                 LDR             D0, [X19]
LOAD:0000000000BEEB84                 B.NE            loc_BEF3BC
LOAD:0000000000BEEB88                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEB8C                 B.LS            loc_BEF3BC
LOAD:0000000000BEEB90                 BL              sub_641D50
LOAD:0000000000BEEB94                 B               loc_BEEB6C
LOAD:0000000000BEEB98 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEB98                 LDR             X0, [X19]
LOAD:0000000000BEEB9C                 CMP             X28, #8
LOAD:0000000000BEEBA0                 LDR             D0, [X19]
LOAD:0000000000BEEBA4                 B.CC            loc_BEF3BC
LOAD:0000000000BEEBA8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEBAC                 B.LS            loc_BEF3BC
LOAD:0000000000BEEBB0                 BL              sub_60CCF0
LOAD:0000000000BEEBB4                 B               loc_BEEB6C
LOAD:0000000000BEEBB8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEBB8                 LDR             X0, [X19]
LOAD:0000000000BEEBBC                 CMP             X28, #8
LOAD:0000000000BEEBC0                 LDR             D0, [X19]
LOAD:0000000000BEEBC4                 B.CC            loc_BEF3BC
LOAD:0000000000BEEBC8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEBCC                 B.LS            loc_BEF3BC
LOAD:0000000000BEEBD0                 BL              sub_62E810
LOAD:0000000000BEEBD4                 B               loc_BEEB6C
LOAD:0000000000BEEBD8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEBD8                 LDR             X0, [X19]
LOAD:0000000000BEEBDC                 CMP             X28, #8
LOAD:0000000000BEEBE0                 LDR             D0, [X19]
LOAD:0000000000BEEBE4                 B.CC            loc_BEF3BC
LOAD:0000000000BEEBE8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEBEC                 B.LS            loc_BEF3BC
LOAD:0000000000BEEBF0                 BL              sub_621CC0
LOAD:0000000000BEEBF4                 B               loc_BEEB6C
LOAD:0000000000BEEBF8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEBF8                 LDR             X0, [X19]
LOAD:0000000000BEEBFC                 CMP             X28, #8
LOAD:0000000000BEEC00                 LDR             D0, [X19]
LOAD:0000000000BEEC04                 B.CC            loc_BEF3BC
LOAD:0000000000BEEC08                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEC0C                 B.LS            loc_BEF3BC
LOAD:0000000000BEEC10                 BL              sub_616040
LOAD:0000000000BEEC14                 B               loc_BEEB6C
LOAD:0000000000BEEC18 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEC18                 LDR             X0, [X19]
LOAD:0000000000BEEC1C                 CMP             X28, #8
LOAD:0000000000BEEC20                 LDR             D0, [X19]
LOAD:0000000000BEEC24                 B.CC            loc_BEF3BC
LOAD:0000000000BEEC28                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEC2C                 B.LS            loc_BEF3BC
LOAD:0000000000BEEC30                 BL              sub_61A020
LOAD:0000000000BEEC34                 B               loc_BEEB6C
LOAD:0000000000BEEC38 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEC38                 LDR             X0, [X19]
LOAD:0000000000BEEC3C                 CMP             X28, #8
LOAD:0000000000BEEC40                 LDR             D0, [X19]
LOAD:0000000000BEEC44                 B.CC            loc_BEF3BC
LOAD:0000000000BEEC48                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEC4C                 B.LS            loc_BEF3BC
LOAD:0000000000BEEC50                 BL              sub_63D960
LOAD:0000000000BEEC54                 B               loc_BEEB6C
LOAD:0000000000BEEC58 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEC58                 LDR             X0, [X19]
LOAD:0000000000BEEC5C                 CMP             X28, #8
LOAD:0000000000BEEC60                 LDR             D0, [X19]
LOAD:0000000000BEEC64                 B.CC            loc_BEF3BC
LOAD:0000000000BEEC68                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEC6C                 B.LS            loc_BEF3BC
LOAD:0000000000BEEC70                 BL              sub_61EA50
LOAD:0000000000BEEC74                 B               loc_BEEB6C
LOAD:0000000000BEEC78 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEC78                 LDR             X0, [X19]
LOAD:0000000000BEEC7C                 CMP             X28, #8
LOAD:0000000000BEEC80                 LDR             D0, [X19]
LOAD:0000000000BEEC84                 B.CC            loc_BEF3BC
LOAD:0000000000BEEC88                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEC8C                 B.LS            loc_BEF3BC
LOAD:0000000000BEEC90                 BL              sub_608FF0
LOAD:0000000000BEEC94                 B               loc_BEEB6C
LOAD:0000000000BEEC98 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEC98                 LDR             X0, [X19]
LOAD:0000000000BEEC9C                 CMP             X28, #8
LOAD:0000000000BEECA0                 LDR             D0, [X19]
LOAD:0000000000BEECA4                 B.CC            loc_BEF3BC
LOAD:0000000000BEECA8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEECAC                 B.LS            loc_BEF3BC
LOAD:0000000000BEECB0                 BL              sub_638ED0
LOAD:0000000000BEECB4                 B               loc_BEEB6C
LOAD:0000000000BEECB8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEECB8                 LDR             X0, [X19]
LOAD:0000000000BEECBC                 CMP             X28, #8
LOAD:0000000000BEECC0                 LDR             D0, [X19]
LOAD:0000000000BEECC4                 B.CC            loc_BEF3BC
LOAD:0000000000BEECC8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEECCC                 B.LS            loc_BEF3BC
LOAD:0000000000BEECD0                 BL              sub_608E10
LOAD:0000000000BEECD4                 B               loc_BEEB6C
LOAD:0000000000BEECD8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEECD8                 LDR             X0, [X19]
LOAD:0000000000BEECDC                 CMP             X28, #8
LOAD:0000000000BEECE0                 LDR             D0, [X19]
LOAD:0000000000BEECE4                 B.CC            loc_BEF3BC
LOAD:0000000000BEECE8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEECEC                 B.LS            loc_BEF3BC
LOAD:0000000000BEECF0                 BL              sub_633FA0
LOAD:0000000000BEECF4                 B               loc_BEEB6C
LOAD:0000000000BEECF8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEECF8                 LDP             X0, X1, [X19]
LOAD:0000000000BEECFC                 CMP             X28, #0x10
LOAD:0000000000BEED00                 LDP             D0, D1, [X19]
LOAD:0000000000BEED04                 B.CC            loc_BEF3BC
LOAD:0000000000BEED08                 CMP             X25, X0,LSR#32
LOAD:0000000000BEED0C                 B.LS            loc_BEF3BC
LOAD:0000000000BEED10                 CMP             X25, X1,LSR#32
LOAD:0000000000BEED14                 B.LS            loc_BEF3BC
LOAD:0000000000BEED18                 BL              sub_62F5D0
LOAD:0000000000BEED1C                 B               loc_BEEB6C
LOAD:0000000000BEED20 ; ---------------------------------------------------------------------------
LOAD:0000000000BEED20                 LDP             X0, X1, [X19]
LOAD:0000000000BEED24                 CMP             X28, #0x10
LOAD:0000000000BEED28                 LDP             D0, D1, [X19]
LOAD:0000000000BEED2C                 B.CC            loc_BEF3BC
LOAD:0000000000BEED30                 CMP             X25, X0,LSR#32
LOAD:0000000000BEED34                 B.LS            loc_BEF3BC
LOAD:0000000000BEED38                 CMP             X25, X1,LSR#32
LOAD:0000000000BEED3C                 B.LS            loc_BEF3BC
LOAD:0000000000BEED40                 BL              sub_6193B0
LOAD:0000000000BEED44                 B               loc_BEEB6C
LOAD:0000000000BEED48 ; ---------------------------------------------------------------------------
LOAD:0000000000BEED48                 LDP             X0, X1, [X19]
LOAD:0000000000BEED4C                 CMP             X28, #0x10
LOAD:0000000000BEED50                 LDP             D0, D1, [X19]
LOAD:0000000000BEED54                 B.CC            loc_BEF3BC
LOAD:0000000000BEED58                 CMP             X25, X0,LSR#32
LOAD:0000000000BEED5C                 B.LS            loc_BEF3BC
LOAD:0000000000BEED60                 CMP             X25, X1,LSR#32
LOAD:0000000000BEED64                 B.LS            loc_BEF3BC
LOAD:0000000000BEED68                 BL              sub_62DE30
LOAD:0000000000BEED6C                 B               loc_BEEB6C
LOAD:0000000000BEED70 ; ---------------------------------------------------------------------------
LOAD:0000000000BEED70                 LDP             X0, X1, [X19]
LOAD:0000000000BEED74                 CMP             X28, #0x10
LOAD:0000000000BEED78                 B.CC            loc_BEF3BC
LOAD:0000000000BEED7C                 LDR             D0, [X19]
LOAD:0000000000BEED80                 CMP             X25, X0,LSR#32
LOAD:0000000000BEED84                 B.LS            loc_BEF3BC
LOAD:0000000000BEED88                 CMP             X25, X1,LSR#32
LOAD:0000000000BEED8C                 B.NE            loc_BEF3BC
LOAD:0000000000BEED90                 SXTW            X0, W1
LOAD:0000000000BEED94                 BL              sub_60D0B0
LOAD:0000000000BEED98                 B               loc_BEEB6C
LOAD:0000000000BEED9C ; ---------------------------------------------------------------------------
LOAD:0000000000BEED9C                 LDR             X0, [X19]
LOAD:0000000000BEEDA0                 CMP             X28, #8
LOAD:0000000000BEEDA4                 LDR             D0, [X19]
LOAD:0000000000BEEDA8                 B.CC            loc_BEF3BC
LOAD:0000000000BEEDAC                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEDB0                 B.LS            loc_BEF3BC
LOAD:0000000000BEEDB4                 ADD             X0, SP, #0x18
LOAD:0000000000BEEDB8                 BL              sub_61AD40
LOAD:0000000000BEEDBC                 LDR             W1, [SP,#0x18]
LOAD:0000000000BEEDC0                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEEDC4                 STUR            D0, [X19,#-0x10]
LOAD:0000000000BEEDC8                 MOV             W28, #0x18
LOAD:0000000000BEEDCC                 ADD             X1, X1, X24
LOAD:0000000000BEEDD0                 STUR            X1, [X19,#-8]
LOAD:0000000000BEEDD4                 B               loc_BEEB00
LOAD:0000000000BEEDD8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEDD8                 LDR             X0, [X19]
LOAD:0000000000BEEDDC                 CMP             X28, #8
LOAD:0000000000BEEDE0                 LDR             D0, [X19]
LOAD:0000000000BEEDE4                 B.CC            loc_BEF3BC
LOAD:0000000000BEEDE8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEDEC                 B.LS            loc_BEF3BC
LOAD:0000000000BEEDF0                 SUB             X0, X19, #0x10
LOAD:0000000000BEEDF4                 LDUR            X21, [X19,#-8]
LOAD:0000000000BEEDF8                 BL              sub_63DAE0
LOAD:0000000000BEEDFC                 MOV             W28, #0x18
LOAD:0000000000BEEE00                 STUR            D0, [X19,#-8]
LOAD:0000000000BEEE04                 B               loc_BEEB00
LOAD:0000000000BEEE08 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEE08                 LDR             X0, [X19]
LOAD:0000000000BEEE0C                 CMP             X28, #8
LOAD:0000000000BEEE10                 B.CC            loc_BEF3BC
LOAD:0000000000BEEE14                 ADD             X17, X19, X28
LOAD:0000000000BEEE18                 ADD             X27, X19, #8
LOAD:0000000000BEEE1C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEE20                 B.NE            loc_BEEE58
LOAD:0000000000BEEE24
LOAD:0000000000BEEE24 loc_BEEE24                              ; CODE XREF: LOAD:0000000000BEEE44↓j
LOAD:0000000000BEEE24                 LDR             X1, [X27]
LOAD:0000000000BEEE28                 CMP             X27, X17
LOAD:0000000000BEEE2C                 B.CS            loc_BEEAF4
LOAD:0000000000BEEE30                 CMP             X25, X1,LSR#32
LOAD:0000000000BEEE34                 B.NE            loc_BEEE48
LOAD:0000000000BEEE38                 CMP             W0, W1
LOAD:0000000000BEEE3C                 ADD             X27, X27, #8
LOAD:0000000000BEEE40                 CSEL            X0, X1, X0, GT
LOAD:0000000000BEEE44                 B               loc_BEEE24
LOAD:0000000000BEEE48 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEE48
LOAD:0000000000BEEE48 loc_BEEE48                              ; CODE XREF: LOAD:0000000000BEEE34↑j
LOAD:0000000000BEEE48                 SCVTF           D0, W0
LOAD:0000000000BEEE4C                 B.CC            loc_BEF3BC
LOAD:0000000000BEEE50                 LDR             D1, [X27]
LOAD:0000000000BEEE54                 B               loc_BEEE78
LOAD:0000000000BEEE58 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEE58
LOAD:0000000000BEEE58 loc_BEEE58                              ; CODE XREF: LOAD:0000000000BEEE20↑j
LOAD:0000000000BEEE58                 LDR             D0, [X19]
LOAD:0000000000BEEE5C                 B.CC            loc_BEF3BC
LOAD:0000000000BEEE60
LOAD:0000000000BEEE60 loc_BEEE60                              ; CODE XREF: LOAD:0000000000BEEE84↓j
LOAD:0000000000BEEE60                 LDR             X1, [X27]
LOAD:0000000000BEEE64                 LDR             D1, [X27]
LOAD:0000000000BEEE68                 CMP             X27, X17
LOAD:0000000000BEEE6C                 B.CS            loc_BEEB6C
LOAD:0000000000BEEE70                 CMP             X25, X1,LSR#32
LOAD:0000000000BEEE74                 B.LS            loc_BEEE88
LOAD:0000000000BEEE78
LOAD:0000000000BEEE78 loc_BEEE78                              ; CODE XREF: LOAD:0000000000BEEE54↑j
LOAD:0000000000BEEE78                                         ; LOAD:0000000000BEEE90↓j
LOAD:0000000000BEEE78                 FCMP            D0, D1
LOAD:0000000000BEEE7C                 ADD             X27, X27, #8
LOAD:0000000000BEEE80                 FCSEL           D0, D1, D0, PL
LOAD:0000000000BEEE84                 B               loc_BEEE60
LOAD:0000000000BEEE88 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEE88
LOAD:0000000000BEEE88 loc_BEEE88                              ; CODE XREF: LOAD:0000000000BEEE74↑j
LOAD:0000000000BEEE88                 SCVTF           D1, W1
LOAD:0000000000BEEE8C                 B.CC            loc_BEF3BC
LOAD:0000000000BEEE90                 B               loc_BEEE78
LOAD:0000000000BEEE94 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEE94                 LDR             X0, [X19]
LOAD:0000000000BEEE98                 CMP             X28, #8
LOAD:0000000000BEEE9C                 B.CC            loc_BEF3BC
LOAD:0000000000BEEEA0                 ADD             X17, X19, X28
LOAD:0000000000BEEEA4                 ADD             X27, X19, #8
LOAD:0000000000BEEEA8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEEAC                 B.NE            loc_BEEEE4
LOAD:0000000000BEEEB0
LOAD:0000000000BEEEB0 loc_BEEEB0                              ; CODE XREF: LOAD:0000000000BEEED0↓j
LOAD:0000000000BEEEB0                 LDR             X1, [X27]
LOAD:0000000000BEEEB4                 CMP             X27, X17
LOAD:0000000000BEEEB8                 B.CS            loc_BEEAF4
LOAD:0000000000BEEEBC                 CMP             X25, X1,LSR#32
LOAD:0000000000BEEEC0                 B.NE            loc_BEEED4
LOAD:0000000000BEEEC4                 CMP             W0, W1
LOAD:0000000000BEEEC8                 ADD             X27, X27, #8
LOAD:0000000000BEEECC                 CSEL            X0, X1, X0, LT
LOAD:0000000000BEEED0                 B               loc_BEEEB0
LOAD:0000000000BEEED4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEED4
LOAD:0000000000BEEED4 loc_BEEED4                              ; CODE XREF: LOAD:0000000000BEEEC0↑j
LOAD:0000000000BEEED4                 SCVTF           D0, W0
LOAD:0000000000BEEED8                 B.CC            loc_BEF3BC
LOAD:0000000000BEEEDC                 LDR             D1, [X27]
LOAD:0000000000BEEEE0                 B               loc_BEEF04
LOAD:0000000000BEEEE4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEEE4
LOAD:0000000000BEEEE4 loc_BEEEE4                              ; CODE XREF: LOAD:0000000000BEEEAC↑j
LOAD:0000000000BEEEE4                 LDR             D0, [X19]
LOAD:0000000000BEEEE8                 B.CC            loc_BEF3BC
LOAD:0000000000BEEEEC
LOAD:0000000000BEEEEC loc_BEEEEC                              ; CODE XREF: LOAD:0000000000BEEF10↓j
LOAD:0000000000BEEEEC                 LDR             X1, [X27]
LOAD:0000000000BEEEF0                 LDR             D1, [X27]
LOAD:0000000000BEEEF4                 CMP             X27, X17
LOAD:0000000000BEEEF8                 B.CS            loc_BEEB6C
LOAD:0000000000BEEEFC                 CMP             X25, X1,LSR#32
LOAD:0000000000BEEF00                 B.LS            loc_BEEF14
LOAD:0000000000BEEF04
LOAD:0000000000BEEF04 loc_BEEF04                              ; CODE XREF: LOAD:0000000000BEEEE0↑j
LOAD:0000000000BEEF04                                         ; LOAD:0000000000BEEF1C↓j
LOAD:0000000000BEEF04                 FCMP            D0, D1
LOAD:0000000000BEEF08                 ADD             X27, X27, #8
LOAD:0000000000BEEF0C                 FCSEL           D0, D1, D0, LE
LOAD:0000000000BEEF10                 B               loc_BEEEEC
LOAD:0000000000BEEF14 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEF14
LOAD:0000000000BEEF14 loc_BEEF14                              ; CODE XREF: LOAD:0000000000BEEF00↑j
LOAD:0000000000BEEF14                 SCVTF           D1, W1
LOAD:0000000000BEEF18                 B.CC            loc_BEF3BC
LOAD:0000000000BEEF1C                 B               loc_BEEF04
LOAD:0000000000BEEF20 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEF20                 LDP             X21, X0, [X19,#-8]
LOAD:0000000000BEEF24                 CMP             X28, #8
LOAD:0000000000BEEF28                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEEF2C                 CCMN            X15, #5, #0, EQ
LOAD:0000000000BEEF30                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEEF34                 B.NE            loc_BEF3BC
LOAD:0000000000BEEF38                 LDRB            W8, [X0,#0x18]
LOAD:0000000000BEEF3C                 LDR             W2, [X0,#0x14]
LOAD:0000000000BEEF40                 ADD             X8, X8, X24
LOAD:0000000000BEEF44                 STUR            X8, [X19,#-0x10]
LOAD:0000000000BEEF48                 MOV             W28, #8
LOAD:0000000000BEEF4C                 CBZ             X2, loc_BEEB00
LOAD:0000000000BEEF50                 B               loc_BEEAFC
LOAD:0000000000BEEF54 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEF54                 LDP             X0, X1, [X22,#0x10]
LOAD:0000000000BEEF58                 CMP             X0, X1
LOAD:0000000000BEEF5C                 B.LT            loc_BEEF64
LOAD:0000000000BEEF60                 BL              sub_BEF464
LOAD:0000000000BEEF64
LOAD:0000000000BEEF64 loc_BEEF64                              ; CODE XREF: LOAD:0000000000BEEF5C↑j
LOAD:0000000000BEEF64                 LDP             X21, X0, [X19,#-8]
LOAD:0000000000BEEF68                 CMP             W0, #0xFF
LOAD:0000000000BEEF6C                 CCMP            X28, #8, #0, LS
LOAD:0000000000BEEF70                 B.NE            loc_BEF3BC
LOAD:0000000000BEEF74                 CMP             X25, X0,LSR#32
LOAD:0000000000BEEF78                 B.NE            loc_BEF3BC
LOAD:0000000000BEEF7C                 MOV             W2, #1
LOAD:0000000000BEEF80                 MOV             X1, X19
LOAD:0000000000BEEF84
LOAD:0000000000BEEF84 loc_BEEF84                              ; CODE XREF: LOAD:0000000000BEF03C↓j
LOAD:0000000000BEEF84                 STR             X19, [X23,#0x20]
LOAD:0000000000BEEF88                 MOV             X0, X23
LOAD:0000000000BEEF8C                 STR             X21, [SP,#8]
LOAD:0000000000BEEF90                 BL              sub_BCA210
LOAD:0000000000BEEF94
LOAD:0000000000BEEF94 loc_BEEF94                              ; CODE XREF: LOAD:0000000000BEF098↓j
LOAD:0000000000BEEF94                                         ; LOAD:0000000000BEF0E4↓j ...
LOAD:0000000000BEEF94                 LDR             X19, [X23,#0x20]
LOAD:0000000000BEEF98                 MOV             X9, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEEF9C                 ADD             X0, X0, X9,LSL#47
LOAD:0000000000BEEFA0                 B               loc_BEEAF4
LOAD:0000000000BEEFA4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEEFA4                 LDP             X0, X1, [X22,#0x10]
LOAD:0000000000BEEFA8                 CMP             X0, X1
LOAD:0000000000BEEFAC                 B.LT            loc_BEEFB4
LOAD:0000000000BEEFB0                 BL              sub_BEF464
LOAD:0000000000BEEFB4
LOAD:0000000000BEEFB4 loc_BEEFB4                              ; CODE XREF: LOAD:0000000000BEEFAC↑j
LOAD:0000000000BEEFB4                 LDR             X0, [X19]
LOAD:0000000000BEEFB8                 LDR             X2, [X19,#0x10]
LOAD:0000000000BEEFBC                 CMP             X28, #0x10
LOAD:0000000000BEEFC0                 MOV             X17, #0xFFFFFFFFFFFFFFFF
LOAD:0000000000BEEFC4                 B.EQ            loc_BEEFD8
LOAD:0000000000BEEFC8                 B.CC            loc_BEF3BC
LOAD:0000000000BEEFCC                 CMP             X25, X2,LSR#32
LOAD:0000000000BEEFD0                 B.NE            loc_BEF3BC
LOAD:0000000000BEEFD4                 SXTW            X17, W2
LOAD:0000000000BEEFD8
LOAD:0000000000BEEFD8 loc_BEEFD8                              ; CODE XREF: LOAD:0000000000BEEFC4↑j
LOAD:0000000000BEEFD8                 LDR             X1, [X19,#8]
LOAD:0000000000BEEFDC                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEEFE0                 CMN             X15, #5
LOAD:0000000000BEEFE4                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEEFE8                 B.NE            loc_BEF3BC
LOAD:0000000000BEEFEC                 LDR             W9, [X0,#0x14]
LOAD:0000000000BEEFF0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEEFF4                 B.NE            loc_BEF3BC
LOAD:0000000000BEEFF8                 SXTW            X1, W1
LOAD:0000000000BEEFFC                 ADD             X10, X17, X9
LOAD:0000000000BEF000                 CMP             X17, #0
LOAD:0000000000BEF004                 ADD             X8, X1, X9
LOAD:0000000000BEF008                 CSINC           X17, X17, X10, GE
LOAD:0000000000BEF00C                 CMP             X1, #0
LOAD:0000000000BEF010                 CSINC           X1, X1, X8, GE
LOAD:0000000000BEF014                 CMP             X17, #0
LOAD:0000000000BEF018                 CSEL            X17, X17, XZR, GE
LOAD:0000000000BEF01C                 CMP             X1, #1
LOAD:0000000000BEF020                 CSINC           X1, X1, XZR, GE
LOAD:0000000000BEF024                 CMP             X17, X9
LOAD:0000000000BEF028                 CSEL            X17, X17, X9, LE
LOAD:0000000000BEF02C                 ADD             X0, X0, #0x17
LOAD:0000000000BEF030                 SUBS            X2, X17, X1
LOAD:0000000000BEF034                 ADD             X1, X0, X1
LOAD:0000000000BEF038                 ADD             X2, X2, #1
LOAD:0000000000BEF03C                 B.GE            loc_BEEF84
LOAD:0000000000BEF040                 ADD             X0, X22, #0x78 ; 'x'
LOAD:0000000000BEF044                 MOV             X9, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEF048                 ADD             X0, X0, X9,LSL#47
LOAD:0000000000BEF04C                 B               loc_BEEAF4
LOAD:0000000000BEF050 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF050                 LDP             X0, X1, [X22,#0x10]
LOAD:0000000000BEF054                 CMP             X0, X1
LOAD:0000000000BEF058                 B.LT            loc_BEF060
LOAD:0000000000BEF05C                 BL              sub_BEF464
LOAD:0000000000BEF060
LOAD:0000000000BEF060 loc_BEF060                              ; CODE XREF: LOAD:0000000000BEF058↑j
LOAD:0000000000BEF060                 LDR             X1, [X19]
LOAD:0000000000BEF064                 CMP             X28, #8
LOAD:0000000000BEF068                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BEF06C                 CCMN            X15, #5, #0, CS
LOAD:0000000000BEF070                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BEF074                 B.NE            loc_BEF3BC
LOAD:0000000000BEF078                 LDR             X8, [X22,#0xD8]
LOAD:0000000000BEF07C                 ADD             X0, X22, #0xC8
LOAD:0000000000BEF080                 STR             X19, [X23,#0x20]
LOAD:0000000000BEF084                 STR             X21, [SP,#8]
LOAD:0000000000BEF088                 STR             X23, [X22,#0xE0]
LOAD:0000000000BEF08C                 STR             X8, [X22,#0xC8]
LOAD:0000000000BEF090                 BL              sub_BF1908
LOAD:0000000000BEF094                 BL              sub_BF1F54
LOAD:0000000000BEF098                 B               loc_BEEF94
LOAD:0000000000BEF09C ; ---------------------------------------------------------------------------
LOAD:0000000000BEF09C                 LDP             X0, X1, [X22,#0x10]
LOAD:0000000000BEF0A0                 CMP             X0, X1
LOAD:0000000000BEF0A4                 B.LT            loc_BEF0AC
LOAD:0000000000BEF0A8                 BL              sub_BEF464
LOAD:0000000000BEF0AC
LOAD:0000000000BEF0AC loc_BEF0AC                              ; CODE XREF: LOAD:0000000000BEF0A4↑j
LOAD:0000000000BEF0AC                 LDR             X1, [X19]
LOAD:0000000000BEF0B0                 CMP             X28, #8
LOAD:0000000000BEF0B4                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BEF0B8                 CCMN            X15, #5, #0, CS
LOAD:0000000000BEF0BC                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BEF0C0                 B.NE            loc_BEF3BC
LOAD:0000000000BEF0C4                 LDR             X8, [X22,#0xD8]
LOAD:0000000000BEF0C8                 ADD             X0, X22, #0xC8
LOAD:0000000000BEF0CC                 STR             X19, [X23,#0x20]
LOAD:0000000000BEF0D0                 STR             X21, [SP,#8]
LOAD:0000000000BEF0D4                 STR             X23, [X22,#0xE0]
LOAD:0000000000BEF0D8                 STR             X8, [X22,#0xC8]
LOAD:0000000000BEF0DC                 BL              sub_BF1974
LOAD:0000000000BEF0E0                 BL              sub_BF1F54
LOAD:0000000000BEF0E4                 B               loc_BEEF94
LOAD:0000000000BEF0E8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF0E8                 LDP             X0, X1, [X22,#0x10]
LOAD:0000000000BEF0EC                 CMP             X0, X1
LOAD:0000000000BEF0F0                 B.LT            loc_BEF0F8
LOAD:0000000000BEF0F4                 BL              sub_BEF464
LOAD:0000000000BEF0F8
LOAD:0000000000BEF0F8 loc_BEF0F8                              ; CODE XREF: LOAD:0000000000BEF0F0↑j
LOAD:0000000000BEF0F8                 LDR             X1, [X19]
LOAD:0000000000BEF0FC                 CMP             X28, #8
LOAD:0000000000BEF100                 ASR             X15, X1, #0x2F ; '/'
LOAD:0000000000BEF104                 CCMN            X15, #5, #0, CS
LOAD:0000000000BEF108                 AND             X1, X1, #0x7FFFFFFFFFFF
LOAD:0000000000BEF10C                 B.NE            loc_BEF3BC
LOAD:0000000000BEF110                 LDR             X8, [X22,#0xD8]
LOAD:0000000000BEF114                 ADD             X0, X22, #0xC8
LOAD:0000000000BEF118                 STR             X19, [X23,#0x20]
LOAD:0000000000BEF11C                 STR             X21, [SP,#8]
LOAD:0000000000BEF120                 STR             X23, [X22,#0xE0]
LOAD:0000000000BEF124                 STR             X8, [X22,#0xC8]
LOAD:0000000000BEF128                 BL              sub_BF1A80
LOAD:0000000000BEF12C                 BL              sub_BF1F54
LOAD:0000000000BEF130                 B               loc_BEEF94
LOAD:0000000000BEF134
LOAD:0000000000BEF134 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEF134
LOAD:0000000000BEF134
LOAD:0000000000BEF134 sub_BEF134                              ; CODE XREF: sub_BEF134+4C↓p
LOAD:0000000000BEF134                                         ; sub_BEF134+70↓p ...
LOAD:0000000000BEF134
LOAD:0000000000BEF134 arg_8           =  8
LOAD:0000000000BEF134 arg_20          =  0x20
LOAD:0000000000BEF134
LOAD:0000000000BEF134 ; FUNCTION CHUNK AT LOAD:0000000000BEEAF4 SIZE 0000005C BYTES
LOAD:0000000000BEF134 ; FUNCTION CHUNK AT LOAD:0000000000BEF430 SIZE 00000020 BYTES
LOAD:0000000000BEF134 ; FUNCTION CHUNK AT LOAD:0000000000BEF450 SIZE 00000014 BYTES
LOAD:0000000000BEF134
LOAD:0000000000BEF134                 B.LS            loc_BEF3BC
LOAD:0000000000BEF138                 ADD             X1, X0, X0
LOAD:0000000000BEF13C                 MOV             W2, #0x434
LOAD:0000000000BEF140                 SUB             X2, X2, X1,LSR#53
LOAD:0000000000BEF144                 CMP             X2, #0x35 ; '5'
LOAD:0000000000BEF148                 B.HI            loc_BEF164
LOAD:0000000000BEF14C                 AND             X1, X1, #0x1FFFFFFFFFFFFF
LOAD:0000000000BEF150                 ORR             X1, X1, #0x20000000000000
LOAD:0000000000BEF154                 CMP             X0, #0
LOAD:0000000000BEF158                 LSR             X1, X1, X2
LOAD:0000000000BEF15C                 CNEG            W0, W1, MI
LOAD:0000000000BEF160                 BR              X30
LOAD:0000000000BEF164 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF164
LOAD:0000000000BEF164 loc_BEF164                              ; CODE XREF: sub_BEF134+14↑j
LOAD:0000000000BEF164                 MOV             W0, #0
LOAD:0000000000BEF168                 BR              X30
LOAD:0000000000BEF16C ; ---------------------------------------------------------------------------
LOAD:0000000000BEF16C                 LDR             X0, [X19]
LOAD:0000000000BEF170                 CMP             X28, #8
LOAD:0000000000BEF174                 B.CC            loc_BEF3BC
LOAD:0000000000BEF178                 ADR             X30, loc_BEF184
LOAD:0000000000BEF17C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF180                 B.NE            sub_BEF134
LOAD:0000000000BEF184
LOAD:0000000000BEF184 loc_BEF184                              ; DATA XREF: sub_BEF134+44↑o
LOAD:0000000000BEF184                 MOV             W27, #8
LOAD:0000000000BEF188                 MOV             W8, W0
LOAD:0000000000BEF18C                 ADR             X30, loc_BEF1A8
LOAD:0000000000BEF190
LOAD:0000000000BEF190 loc_BEF190                              ; CODE XREF: sub_BEF134+78↓j
LOAD:0000000000BEF190                 LDR             X0, [X19,X27]
LOAD:0000000000BEF194                 CMP             X27, X28
LOAD:0000000000BEF198                 ADD             X27, X27, #8
LOAD:0000000000BEF19C                 B.GE            loc_BEF254
LOAD:0000000000BEF1A0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF1A4                 B.NE            sub_BEF134
LOAD:0000000000BEF1A8
LOAD:0000000000BEF1A8 loc_BEF1A8                              ; DATA XREF: sub_BEF134+58↑o
LOAD:0000000000BEF1A8                 AND             W8, W8, W0
LOAD:0000000000BEF1AC                 B               loc_BEF190
LOAD:0000000000BEF1B0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF1B0                 LDR             X0, [X19]
LOAD:0000000000BEF1B4                 CMP             X28, #8
LOAD:0000000000BEF1B8                 B.CC            loc_BEF3BC
LOAD:0000000000BEF1BC                 ADR             X30, loc_BEF1C8
LOAD:0000000000BEF1C0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF1C4                 B.NE            sub_BEF134
LOAD:0000000000BEF1C8
LOAD:0000000000BEF1C8 loc_BEF1C8                              ; DATA XREF: sub_BEF134+88↑o
LOAD:0000000000BEF1C8                 MOV             W27, #8
LOAD:0000000000BEF1CC                 MOV             W8, W0
LOAD:0000000000BEF1D0                 ADR             X30, loc_BEF1EC
LOAD:0000000000BEF1D4
LOAD:0000000000BEF1D4 loc_BEF1D4                              ; CODE XREF: sub_BEF134+BC↓j
LOAD:0000000000BEF1D4                 LDR             X0, [X19,X27]
LOAD:0000000000BEF1D8                 CMP             X27, X28
LOAD:0000000000BEF1DC                 ADD             X27, X27, #8
LOAD:0000000000BEF1E0                 B.GE            loc_BEF254
LOAD:0000000000BEF1E4                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF1E8                 B.NE            sub_BEF134
LOAD:0000000000BEF1EC
LOAD:0000000000BEF1EC loc_BEF1EC                              ; DATA XREF: sub_BEF134+9C↑o
LOAD:0000000000BEF1EC                 ORR             W8, W8, W0
LOAD:0000000000BEF1F0                 B               loc_BEF1D4
LOAD:0000000000BEF1F4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF1F4                 LDR             X0, [X19]
LOAD:0000000000BEF1F8                 CMP             X28, #8
LOAD:0000000000BEF1FC                 B.CC            loc_BEF3BC
LOAD:0000000000BEF200                 ADR             X30, loc_BEF20C
LOAD:0000000000BEF204                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF208                 B.NE            sub_BEF134
LOAD:0000000000BEF20C
LOAD:0000000000BEF20C loc_BEF20C                              ; DATA XREF: sub_BEF134+CC↑o
LOAD:0000000000BEF20C                 MOV             W27, #8
LOAD:0000000000BEF210                 MOV             W8, W0
LOAD:0000000000BEF214                 ADR             X30, loc_BEF230
LOAD:0000000000BEF218
LOAD:0000000000BEF218 loc_BEF218                              ; CODE XREF: sub_BEF134+100↓j
LOAD:0000000000BEF218                 LDR             X0, [X19,X27]
LOAD:0000000000BEF21C                 CMP             X27, X28
LOAD:0000000000BEF220                 ADD             X27, X27, #8
LOAD:0000000000BEF224                 B.GE            loc_BEF254
LOAD:0000000000BEF228                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF22C                 B.NE            sub_BEF134
LOAD:0000000000BEF230
LOAD:0000000000BEF230 loc_BEF230                              ; DATA XREF: sub_BEF134+E0↑o
LOAD:0000000000BEF230                 EOR             W8, W8, W0
LOAD:0000000000BEF234                 B               loc_BEF218
LOAD:0000000000BEF238 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF238                 LDR             X0, [X19]
LOAD:0000000000BEF23C                 CMP             X28, #8
LOAD:0000000000BEF240                 B.CC            loc_BEF3BC
LOAD:0000000000BEF244                 ADR             X30, loc_BEF250
LOAD:0000000000BEF248                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF24C                 B.NE            sub_BEF134
LOAD:0000000000BEF250
LOAD:0000000000BEF250 loc_BEF250                              ; DATA XREF: sub_BEF134+110↑o
LOAD:0000000000BEF250                 MOV             W8, W0
LOAD:0000000000BEF254
LOAD:0000000000BEF254 loc_BEF254                              ; CODE XREF: sub_BEF134+68↑j
LOAD:0000000000BEF254                                         ; sub_BEF134+AC↑j ...
LOAD:0000000000BEF254                 ADD             X0, X8, X24
LOAD:0000000000BEF258                 B               loc_BEEAF4
LOAD:0000000000BEF25C ; ---------------------------------------------------------------------------
LOAD:0000000000BEF25C                 LDR             X0, [X19]
LOAD:0000000000BEF260                 CMP             X28, #8
LOAD:0000000000BEF264                 B.CC            loc_BEF3BC
LOAD:0000000000BEF268                 ADR             X30, loc_BEF274
LOAD:0000000000BEF26C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF270                 B.NE            sub_BEF134
LOAD:0000000000BEF274
LOAD:0000000000BEF274 loc_BEF274                              ; DATA XREF: sub_BEF134+134↑o
LOAD:0000000000BEF274                 REV             W8, W0
LOAD:0000000000BEF278                 ADD             X0, X8, X24
LOAD:0000000000BEF27C                 B               loc_BEEAF4
LOAD:0000000000BEF280 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF280                 LDR             X0, [X19]
LOAD:0000000000BEF284                 CMP             X28, #8
LOAD:0000000000BEF288                 B.CC            loc_BEF3BC
LOAD:0000000000BEF28C                 ADR             X30, loc_BEF298
LOAD:0000000000BEF290                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF294                 B.NE            sub_BEF134
LOAD:0000000000BEF298
LOAD:0000000000BEF298 loc_BEF298                              ; DATA XREF: sub_BEF134+158↑o
LOAD:0000000000BEF298                 MVN             W8, W0
LOAD:0000000000BEF29C                 ADD             X0, X8, X24
LOAD:0000000000BEF2A0                 B               loc_BEEAF4
LOAD:0000000000BEF2A4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF2A4                 LDP             X8, X0, [X19]
LOAD:0000000000BEF2A8                 CMP             X28, #0x10
LOAD:0000000000BEF2AC                 B.CC            loc_BEF3BC
LOAD:0000000000BEF2B0                 ADR             X30, loc_BEF2BC
LOAD:0000000000BEF2B4                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF2B8                 B.NE            sub_BEF134
LOAD:0000000000BEF2BC
LOAD:0000000000BEF2BC loc_BEF2BC                              ; DATA XREF: sub_BEF134+17C↑o
LOAD:0000000000BEF2BC                 MOV             X9, X0
LOAD:0000000000BEF2C0                 MOV             X0, X8
LOAD:0000000000BEF2C4                 ADR             X30, loc_BEF2D0
LOAD:0000000000BEF2C8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF2CC                 B.NE            sub_BEF134
LOAD:0000000000BEF2D0
LOAD:0000000000BEF2D0 loc_BEF2D0                              ; DATA XREF: sub_BEF134+190↑o
LOAD:0000000000BEF2D0                 LSL             W8, W0, W9
LOAD:0000000000BEF2D4                 ADD             X0, X8, X24
LOAD:0000000000BEF2D8                 B               loc_BEEAF4
LOAD:0000000000BEF2DC ; ---------------------------------------------------------------------------
LOAD:0000000000BEF2DC                 LDP             X8, X0, [X19]
LOAD:0000000000BEF2E0                 CMP             X28, #0x10
LOAD:0000000000BEF2E4                 B.CC            loc_BEF3BC
LOAD:0000000000BEF2E8                 ADR             X30, loc_BEF2F4
LOAD:0000000000BEF2EC                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF2F0                 B.NE            sub_BEF134
LOAD:0000000000BEF2F4
LOAD:0000000000BEF2F4 loc_BEF2F4                              ; DATA XREF: sub_BEF134+1B4↑o
LOAD:0000000000BEF2F4                 MOV             X9, X0
LOAD:0000000000BEF2F8                 MOV             X0, X8
LOAD:0000000000BEF2FC                 ADR             X30, loc_BEF308
LOAD:0000000000BEF300                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF304                 B.NE            sub_BEF134
LOAD:0000000000BEF308
LOAD:0000000000BEF308 loc_BEF308                              ; DATA XREF: sub_BEF134+1C8↑o
LOAD:0000000000BEF308                 LSR             W8, W0, W9
LOAD:0000000000BEF30C                 ADD             X0, X8, X24
LOAD:0000000000BEF310                 B               loc_BEEAF4
LOAD:0000000000BEF314 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF314                 LDP             X8, X0, [X19]
LOAD:0000000000BEF318                 CMP             X28, #0x10
LOAD:0000000000BEF31C                 B.CC            loc_BEF3BC
LOAD:0000000000BEF320                 ADR             X30, loc_BEF32C
LOAD:0000000000BEF324                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF328                 B.NE            sub_BEF134
LOAD:0000000000BEF32C
LOAD:0000000000BEF32C loc_BEF32C                              ; DATA XREF: sub_BEF134+1EC↑o
LOAD:0000000000BEF32C                 MOV             X9, X0
LOAD:0000000000BEF330                 MOV             X0, X8
LOAD:0000000000BEF334                 ADR             X30, loc_BEF340
LOAD:0000000000BEF338                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF33C                 B.NE            sub_BEF134
LOAD:0000000000BEF340
LOAD:0000000000BEF340 loc_BEF340                              ; DATA XREF: sub_BEF134+200↑o
LOAD:0000000000BEF340                 ASR             W8, W0, W9
LOAD:0000000000BEF344                 ADD             X0, X8, X24
LOAD:0000000000BEF348                 B               loc_BEEAF4
LOAD:0000000000BEF34C ; ---------------------------------------------------------------------------
LOAD:0000000000BEF34C                 LDP             X8, X0, [X19]
LOAD:0000000000BEF350                 CMP             X28, #0x10
LOAD:0000000000BEF354                 B.CC            loc_BEF3BC
LOAD:0000000000BEF358                 ADR             X30, loc_BEF364
LOAD:0000000000BEF35C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF360                 B.NE            sub_BEF134
LOAD:0000000000BEF364
LOAD:0000000000BEF364 loc_BEF364                              ; DATA XREF: sub_BEF134+224↑o
LOAD:0000000000BEF364                 NEG             X9, X0
LOAD:0000000000BEF368                 MOV             X0, X8
LOAD:0000000000BEF36C                 ADR             X30, loc_BEF378
LOAD:0000000000BEF370                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF374                 B.NE            sub_BEF134
LOAD:0000000000BEF378
LOAD:0000000000BEF378 loc_BEF378                              ; DATA XREF: sub_BEF134+238↑o
LOAD:0000000000BEF378                 ROR             W8, W0, W9
LOAD:0000000000BEF37C                 ADD             X0, X8, X24
LOAD:0000000000BEF380                 B               loc_BEEAF4
LOAD:0000000000BEF384 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF384                 LDP             X8, X0, [X19]
LOAD:0000000000BEF388                 CMP             X28, #0x10
LOAD:0000000000BEF38C                 B.CC            loc_BEF3BC
LOAD:0000000000BEF390                 ADR             X30, loc_BEF39C
LOAD:0000000000BEF394                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF398                 B.NE            sub_BEF134
LOAD:0000000000BEF39C
LOAD:0000000000BEF39C loc_BEF39C                              ; DATA XREF: sub_BEF134+25C↑o
LOAD:0000000000BEF39C                 MOV             X9, X0
LOAD:0000000000BEF3A0                 MOV             X0, X8
LOAD:0000000000BEF3A4                 ADR             X30, loc_BEF3B0
LOAD:0000000000BEF3A8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEF3AC                 B.NE            sub_BEF134
LOAD:0000000000BEF3B0
LOAD:0000000000BEF3B0 loc_BEF3B0                              ; DATA XREF: sub_BEF134+270↑o
LOAD:0000000000BEF3B0                 ROR             W8, W0, W9
LOAD:0000000000BEF3B4                 ADD             X0, X8, X24
LOAD:0000000000BEF3B8                 B               loc_BEEAF4
LOAD:0000000000BEF3BC ; ---------------------------------------------------------------------------
LOAD:0000000000BEF3BC
LOAD:0000000000BEF3BC loc_BEF3BC                              ; CODE XREF: LOAD:0000000000BEE488↑j
LOAD:0000000000BEF3BC                                         ; LOAD:0000000000BEE498↑j ...
LOAD:0000000000BEF3BC                 LDP             X2, X21, [X19,#-0x10]
LOAD:0000000000BEF3C0                 LDR             X10, [X23,#0x30]
LOAD:0000000000BEF3C4                 ADD             X9, X19, X28
LOAD:0000000000BEF3C8                 STP             X19, X9, [X23,#0x20]
LOAD:0000000000BEF3CC                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEF3D0                 ADD             X9, X9, #0xA0
LOAD:0000000000BEF3D4                 LDR             X2, [X2,#0x28]
LOAD:0000000000BEF3D8                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEF3DC                 CMP             X9, X10
LOAD:0000000000BEF3E0                 MOV             X0, X23
LOAD:0000000000BEF3E4                 B.HI            loc_BEF450
LOAD:0000000000BEF3E8                 BLR             X2
LOAD:0000000000BEF3EC                 LDR             X19, [X23,#0x20]
LOAD:0000000000BEF3F0                 CMP             W0, #0
LOAD:0000000000BEF3F4                 LSL             X28, X0, #3
LOAD:0000000000BEF3F8                 SUB             X27, X19, #0x10
LOAD:0000000000BEF3FC                 B.GT            loc_BEEB00
LOAD:0000000000BEF400
LOAD:0000000000BEF400 loc_BEF400                              ; CODE XREF: sub_BEF134+32C↓j
LOAD:0000000000BEF400                 LDR             X0, [X23,#0x28]
LOAD:0000000000BEF404                 LDUR            X2, [X19,#-0x10]
LOAD:0000000000BEF408                 SUB             X28, X0, X19
LOAD:0000000000BEF40C                 B.NE            loc_BEF430
LOAD:0000000000BEF410                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEF414                 LDR             X21, [X2,#0x20]
LOAD:0000000000BEF418                 LDR             W16, [X21],#4
LOAD:0000000000BEF41C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEF420                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEF424                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEF428                 ADD             X27, X19, X27,LSL#3
LOAD:0000000000BEF42C                 BR              X8
LOAD:0000000000BEF42C ; End of function sub_BEF134
LOAD:0000000000BEF42C
LOAD:0000000000BEF430 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF430 ; START OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BEF430 ;   ADDITIONAL PARENT FUNCTION sub_BEF134
LOAD:0000000000BEF430
LOAD:0000000000BEF430 loc_BEF430                              ; CODE XREF: sub_BEDE10+2D8↑j
LOAD:0000000000BEF430                                         ; sub_BEF134+2D8↑j
LOAD:0000000000BEF430                 ANDS            X8, X21, #3
LOAD:0000000000BEF434                 AND             X9, X21, #0xFFFFFFFFFFFFFFF8
LOAD:0000000000BEF438                 B.NE            loc_BEF448
LOAD:0000000000BEF43C                 LDURB           W27, [X21,#-3]
LOAD:0000000000BEF440                 LSL             X27, X27, #3
LOAD:0000000000BEF444                 ADD             X9, X27, #0x10
LOAD:0000000000BEF448
LOAD:0000000000BEF448 loc_BEF448                              ; CODE XREF: sub_BEDE10+1628↑j
LOAD:0000000000BEF448                 SUB             X17, X19, X9
LOAD:0000000000BEF44C                 B               loc_BEDFF8
LOAD:0000000000BEF44C ; END OF FUNCTION CHUNK FOR sub_BEDE10
LOAD:0000000000BEF450 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF450 ; START OF FUNCTION CHUNK FOR sub_BEF134
LOAD:0000000000BEF450
LOAD:0000000000BEF450 loc_BEF450                              ; CODE XREF: sub_BEF134+2B0↑j
LOAD:0000000000BEF450                 MOV             W1, #0x14
LOAD:0000000000BEF454                 BL              sub_BCCCCC
LOAD:0000000000BEF458                 LDR             X19, [X23,#0x20]
LOAD:0000000000BEF45C                 CMP             X0, X0
LOAD:0000000000BEF460                 B               loc_BEF400
LOAD:0000000000BEF460 ; END OF FUNCTION CHUNK FOR sub_BEF134
LOAD:0000000000BEF464
LOAD:0000000000BEF464 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEF464
LOAD:0000000000BEF464
LOAD:0000000000BEF464 sub_BEF464                              ; CODE XREF: LOAD:0000000000BEE65C↑p
LOAD:0000000000BEF464                                         ; LOAD:0000000000BEEF60↑p ...
LOAD:0000000000BEF464
LOAD:0000000000BEF464 arg_8           =  8
LOAD:0000000000BEF464
LOAD:0000000000BEF464                 ADD             X1, X19, X28
LOAD:0000000000BEF468                 MOV             X27, X30
LOAD:0000000000BEF46C                 STP             X19, X1, [X23,#0x20]
LOAD:0000000000BEF470                 STR             X21, [SP,#arg_8]
LOAD:0000000000BEF474                 MOV             X0, X23
LOAD:0000000000BEF478                 BL              sub_BEFFCC
LOAD:0000000000BEF47C                 LDP             X19, X1, [X23,#0x20]
LOAD:0000000000BEF480                 LDUR            X2, [X19,#-0x10]
LOAD:0000000000BEF484                 MOV             X30, X27
LOAD:0000000000BEF488                 SUB             X28, X1, X19
LOAD:0000000000BEF48C                 AND             X2, X2, #0x7FFFFFFFFFFF
LOAD:0000000000BEF490                 RET
LOAD:0000000000BEF490 ; End of function sub_BEF464
LOAD:0000000000BEF490
LOAD:0000000000BEF494 ; ---------------------------------------------------------------------------
LOAD:0000000000BEF494
LOAD:0000000000BEF494 loc_BEF494                              ; DATA XREF: sub_BCD558+114↑o
LOAD:0000000000BEF494                                         ; sub_BCD558+11C↑o
LOAD:0000000000BEF494                 LDRB            W0, [X22,#0x91]
LOAD:0000000000BEF498                 TST             X0, #0x20
LOAD:0000000000BEF49C                 B.NE            loc_BEF4C8
LOAD:0000000000BEF4A0                 LDR             W1, [X22,#0x148]
LOAD:0000000000BEF4A4                 TST             X0, #0x10
LOAD:0000000000BEF4A8                 B.NE            loc_BEF4F4
LOAD:0000000000BEF4AC                 SUB             W1, W1, #1
LOAD:0000000000BEF4B0                 TST             X0, #0xC
LOAD:0000000000BEF4B4                 B.EQ            loc_BEF4F4
LOAD:0000000000BEF4B8                 STR             W1, [X22,#0x148]
LOAD:0000000000BEF4BC                 B               loc_BEF4F4
LOAD:0000000000BEF4C0
LOAD:0000000000BEF4C0 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEF4C0
LOAD:0000000000BEF4C0
LOAD:0000000000BEF4C0 sub_BEF4C0                              ; DATA XREF: sub_BCD558:loc_BCD6CC↑o
LOAD:0000000000BEF4C0                 LDRB            W10, [X22,#0x91]
LOAD:0000000000BEF4C4                 TBZ             W10, #4, loc_BEF4F4
LOAD:0000000000BEF4C4 ; End of function sub_BEF4C0
LOAD:0000000000BEF4C4
LOAD:0000000000BEF4C8 ; START OF FUNCTION CHUNK FOR sub_BEF4D0
LOAD:0000000000BEF4C8
LOAD:0000000000BEF4C8 loc_BEF4C8                              ; CODE XREF: LOAD:0000000000BEF49C↑j
LOAD:0000000000BEF4C8                                         ; sub_BEF4D0+8↓j ...
LOAD:0000000000BEF4C8                 LDR             X8, [X9,#0x14C0]
LOAD:0000000000BEF4CC                 BR              X8
LOAD:0000000000BEF4CC ; END OF FUNCTION CHUNK FOR sub_BEF4D0
LOAD:0000000000BEF4D0
LOAD:0000000000BEF4D0 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEF4D0
LOAD:0000000000BEF4D0
LOAD:0000000000BEF4D0 sub_BEF4D0                              ; DATA XREF: sub_BCD558+118↑o
LOAD:0000000000BEF4D0                                         ; sub_BCD558+120↑o
LOAD:0000000000BEF4D0
LOAD:0000000000BEF4D0 ; FUNCTION CHUNK AT LOAD:0000000000BEF4C8 SIZE 00000008 BYTES
LOAD:0000000000BEF4D0
LOAD:0000000000BEF4D0                 LDRB            W10, [X22,#0x91]
LOAD:0000000000BEF4D4                 LDR             W11, [X22,#0x148]
LOAD:0000000000BEF4D8                 TBNZ            W10, #4, loc_BEF4C8
LOAD:0000000000BEF4DC                 TST             W10, #0xC
LOAD:0000000000BEF4E0                 B.EQ            loc_BEF4C8
LOAD:0000000000BEF4E4                 SUB             W11, W11, #1
LOAD:0000000000BEF4E8                 STR             W11, [X22,#0x148]
LOAD:0000000000BEF4EC                 CBZ             W11, loc_BEF4F4
LOAD:0000000000BEF4F0                 TBZ             W10, #2, loc_BEF4C8
LOAD:0000000000BEF4F4
LOAD:0000000000BEF4F4 loc_BEF4F4                              ; CODE XREF: LOAD:0000000000BEF4A8↑j
LOAD:0000000000BEF4F4                                         ; LOAD:0000000000BEF4B4↑j ...
LOAD:0000000000BEF4F4                 MOV             X0, X23
LOAD:0000000000BEF4F8                 STR             X19, [X23,#0x20]
LOAD:0000000000BEF4FC                 MOV             X1, X21
LOAD:0000000000BEF500                 BL              sub_BCDC30
LOAD:0000000000BEF504
LOAD:0000000000BEF504 loc_BEF504                              ; CODE XREF: hotcheck+2108↓j
LOAD:0000000000BEF504                 LDR             X19, [X23,#0x20]
LOAD:0000000000BEF508
LOAD:0000000000BEF508 loc_BEF508                              ; CODE XREF: sub_BEF520+C↓j
LOAD:0000000000BEF508                 LDUR            W16, [X21,#-4]
LOAD:0000000000BEF50C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEF510                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEF514                 LDR             X8, [X9,#0x14C0]
LOAD:0000000000BEF518                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEF51C                 BR              X8
LOAD:0000000000BEF51C ; End of function sub_BEF4D0