#!/usr/bin/env python3

import re

def extract_function_addresses():
    """提取所有函数起始地址，按顺序排列"""
    addresses = []
    
    with open('ida pro opcode.txt', 'r') as f:
        for line in f:
            # 查找函数起始地址模式
            if 'LOAD:' in line and ('BC_' in line or 'hotcheck' in line or line.strip().endswith('RET')):
                addr_match = re.search(r'LOAD:([0-9A-F]+)', line)
                if addr_match:
                    address = addr_match.group(1)
                    # 过滤掉一些明显不是opcode的地址
                    if not any(x in line for x in ['End of function', 'CHUNK', 'CODE XREF']):
                        addresses.append(address)
    
    # 去重并排序
    unique_addresses = sorted(list(set(addresses)))
    return unique_addresses

if __name__ == "__main__":
    addresses = extract_function_addresses()
    print("找到的函数地址:")
    for i, addr in enumerate(addresses):
        print(f"0x{i:02x}: 0x{addr}")
    print(f"\n总共找到 {len(addresses)} 个地址")
