-- Test script with various Lua features to generate different opcodes

-- Basic variables and constants
local a = 1
local b = 2.5
local c = "hello"
local d = true
local e = false
local f = nil

-- Arithmetic operations
local sum = a + b
local diff = a - b
local mult = a * b
local div = a / b
local mod = a % b
local pow = a ^ b

-- String operations
local str1 = "world"
local concat = c .. str1
local len = #c

-- Table operations
local t = {}
t[1] = "first"
t["key"] = "value"
t.field = 42

local t2 = {x = 10, y = 20, z = 30}
local val = t2.x
local val2 = t2["y"]

-- Array operations
local arr = {1, 2, 3, 4, 5}
local first = arr[1]
arr[2] = 99

-- Comparison operations
local eq = (a == b)
local ne = (a ~= b)
local lt = (a < b)
local le = (a <= b)
local gt = (a > b)
local ge = (a >= b)

-- Logical operations
local and_op = d and e
local or_op = d or e
local not_op = not d

-- Type checking
local is_num = type(a) == "number"
local is_str = type(c) == "string"

-- Functions
function simple_func(x, y)
    return x + y
end

local function local_func(x)
    return x * 2
end

-- Function calls
local result1 = simple_func(1, 2)
local result2 = local_func(5)

-- Closures and upvalues
function make_counter()
    local count = 0
    return function()
        count = count + 1
        return count
    end
end

local counter = make_counter()
local count1 = counter()
local count2 = counter()

-- Loops
for i = 1, 10 do
    print(i)
end

for i = 1, 10, 2 do
    print("step:", i)
end

for k, v in pairs(t2) do
    print(k, v)
end

for i, v in ipairs(arr) do
    print(i, v)
end

local i = 1
while i <= 5 do
    print("while:", i)
    i = i + 1
end

repeat
    print("repeat:", i)
    i = i - 1
until i <= 0

-- Conditionals
if a > b then
    print("a is greater")
elseif a < b then
    print("a is smaller")
else
    print("a equals b")
end

-- Varargs function
function varargs_func(...)
    local args = {...}
    return #args
end

local arg_count = varargs_func(1, 2, 3, "test")

-- Metamethods
local mt = {
    __add = function(a, b)
        return {value = a.value + b.value}
    end,
    __tostring = function(obj)
        return "Object: " .. obj.value
    end
}

local obj1 = setmetatable({value = 10}, mt)
local obj2 = setmetatable({value = 20}, mt)
local obj3 = obj1 + obj2

-- Error handling
local success, err = pcall(function()
    error("test error")
end)

-- Coroutines
local co = coroutine.create(function()
    for i = 1, 3 do
        coroutine.yield(i)
    end
end)

local ok, val = coroutine.resume(co)

-- Global access
_G.global_var = "global value"
local global_val = _G.global_var

-- Math operations
local sqrt_val = math.sqrt(16)
local sin_val = math.sin(math.pi / 2)
local random_val = math.random()

-- String operations
local upper = string.upper(c)
local lower = string.lower(upper)
local sub = string.sub(c, 1, 2)

-- Table operations
table.insert(arr, 6)
local last = table.remove(arr)
table.sort(arr)

-- Return multiple values
function multi_return()
    return 1, 2, 3
end

local x, y, z = multi_return()

-- Tail call
function tail_call_func(n)
    if n <= 0 then
        return 0
    else
        return tail_call_func(n - 1)
    end
end

local tail_result = tail_call_func(5)

print("Test completed successfully!")
