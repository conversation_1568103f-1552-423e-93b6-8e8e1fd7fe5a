# LuaJIT Opcode Mapping
# Based on modified LuaJIT with custom opcode order
# Format: 0xIndex: 0xAddress = BC_OPCODE_NAME

0x0: 0x0000000000BEBC10 = BC_ISLT
0x1: 0x0000000000BEBC90 = BC_ISGE
0x2: 0x0000000000BEBD10 = BC_ISLE
0x3: 0x0000000000BEBD90 = BC_ISGT
0x4: 0x0000000000BEBE10 = BC_ISEQV
0x5: 0x0000000000BEBE94 = BC_ISNEV
0x6: 0x0000000000BEBF14 = BC_ISEQS
0x7: 0x0000000000BEBF64 = BC_ISNES
0x8: 0x0000000000BEBFB4 = BC_ISEQN
0x9: 0x0000000000BEC040 = BC_ISNEN
0xa: 0x0000000000BEC0CC = BC_ISEQP
0xb: 0x0000000000BEC110 = BC_ISNEP
0xc: 0x0000000000BEC154 = BC_ISFC
0xd: 0x0000000000BEC194 = BC_ISTC
0xe: 0x0000000000BEC1D4 = BC_ISF
0xf: 0x0000000000BEC20C = BC_IST
0x10: 0x0000000000BEC244 = BC_ISTYPE
0x11: 0x0000000000BEC268 = BC_ISNUM
0x12: 0x0000000000BEC28C = BC_NOT
0x13: 0x0000000000BEC2BC = BC_MOV
0x14: 0x0000000000BEC2DC = BC_LEN
0x15: 0x0000000000BEC324 = BC_UNM
0x16: 0x0000000000BEC368 = BC_ADDVN
0x17: 0x0000000000BEC3D4 = BC_SUBVN
0x18: 0x0000000000BEC440 = BC_MULVN
0x19: 0x0000000000BEC4B4 = BC_DIVVN
0x1a: 0x0000000000BEC4FC = BC_MODVN
0x1b: 0x0000000000BEC570 = BC_ADDNV
0x1c: 0x0000000000BEC5DC = BC_SUBNV
0x1d: 0x0000000000BEC648 = BC_MULNV
0x1e: 0x0000000000BEC6BC = BC_DIVNV
0x1f: 0x0000000000BEC704 = BC_MODNV
0x20: 0x0000000000BEC778 = BC_ADDVV
0x21: 0x0000000000BEC7E4 = BC_SUBVV
0x22: 0x0000000000BEC850 = BC_MULVV
0x23: 0x0000000000BEC8C4 = BC_DIVVV
0x24: 0x0000000000BEC90C = BC_MODVV
0x25: 0x0000000000BEC980 = BC_POW
0x26: 0x0000000000BEC9C8 = BC_CAT
0x27: 0x0000000000BECA14 = BC_UGET
0x28: 0x0000000000BECA48 = BC_USETV
0x29: 0x0000000000BECAB8 = BC_USETS
0x2a: 0x0000000000BECB20 = BC_USETN
0x2b: 0x0000000000BECB54 = BC_USETP
0x2c: 0x0000000000BECB88 = BC_UCLO
0x2d: 0x0000000000BECBC4 = BC_FNEW
0x2e: 0x0000000000BECC0C = BC_KSTR
0x2f: 0x0000000000BECC38 = BC_KCDATA
0x30: 0x0000000000BECC64 = BC_KSHORT
0x31: 0x0000000000BECC88 = BC_KNUM
0x32: 0x0000000000BECCA8 = BC_KPRI
0x33: 0x0000000000BECCC8 = BC_KNIL
0x34: 0x0000000000BECCF8 = BC_TNEW
0x35: 0x0000000000BECD58 = BC_TDUP
0x36: 0x0000000000BECDAC = BC_GGET
0x37: 0x0000000000BECDC4 = BC_GSET
0x38: 0x0000000000BECDDC = BC_TGETV
0x39: 0x0000000000BECDC0 = BC_TGETS
0x3a: 0x0000000000BECEFC = BC_TGETB
0x3b: 0x0000000000BECF68 = BC_TGETR
0x3c: 0x0000000000BECFB0 = BC_TSETV
0x3d: 0x0000000000BECDD8 = BC_TSETS
0x3e: 0x0000000000BED15C = BC_TSETB
0x3f: 0x0000000000BED1EC = BC_TSETM
0x40: 0x0000000000BED280 = BC_TSETR
0x41: 0x0000000000BED2E8 = BC_CALLM
0x42: 0x0000000000BED2F4 = BC_CALL
0x43: 0x0000000000BED340 = BC_CALLMT
0x44: 0x0000000000BED34C = BC_CALLT
0x45: 0x0000000000BED408 = BC_ITERC
0x46: 0x0000000000BED474 = BC_ITERN
0x47: 0x0000000000BED518 = BC_VARG
0x48: 0x0000000000BED5D4 = BC_ISNEXT
0x49: 0x0000000000BED674 = BC_RETM
0x4a: 0x0000000000BED688 = BC_RET
0x4b: 0x0000000000BED718 = BC_RET0
0x4c: 0x0000000000BED718 = BC_RET1
0x4d: 0x0000000000BED808 = BC_FORI
0x4e: 0x0000000000BED898 = BC_JFORL
0x4f: 0x0000000000BED934 = BC_FORL
0x50: 0x0000000000BED9DC = BC_IFORL
0x51: 0x0000000000BED898 = BC_JFORL
0x52: 0x0000000000BEDA60 = BC_ITERL
0x53: 0x0000000000BEDAB0 = BC_IITERL
0x54: 0x???????? = BC_JITERL  # NOT FOUND
0x55: 0x0000000000BEDAE0 = BC_LOOP
0x56: 0x???????? = BC_ILOOP  # NOT FOUND
0x57: 0x0000000000BED8D8 = BC_JLOOP
0x58: 0x0000000000BEDB38 = BC_JMP
0x59: 0x0000000000BEDB58 = BC_FUNCF
0x5a: 0x0000000000BEDB14 = BC_IFUNCF
0x5b: 0x0000000000BEDBE4 = BC_JFUNCF
0x5c: 0x???????? = BC_FUNCV  # NOT FOUND
0x5d: 0x???????? = BC_IFUNCV  # NOT FOUND
0x5e: 0x???????? = BC_JFUNCV  # NOT FOUND
0x5f: 0x0000000000BEDC68 = BC_FUNCC
0x60: 0x0000000000BEDCB4 = BC_FUNCCW
