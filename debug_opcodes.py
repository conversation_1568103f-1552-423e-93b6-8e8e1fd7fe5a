#!/usr/bin/env python3

import ljd
import ljd.rawdump.code
import ljd.bytecode.instructions as ins

# Test the opcode initialization
ljd.CURRENT_VERSION = 2.1

from ljd.rawdump.luajit.v2_1.luajit_opcode import _OPCODES as opcodes

print("Initializing opcodes...")
ljd.rawdump.code.init(opcodes)

# Check all instruction opcodes
print("\nChecking all instruction opcodes:")

instruction_list = [
    ins.ISLT, ins.ISGE, ins.ISLE, ins.ISGT,
    ins.ISEQV, ins.ISNEV, ins.ISEQS, ins.ISNES,
    ins.ISEQN, ins.ISNEN, ins.ISEQP, ins.ISNEP,
    ins.ISTC, ins.ISFC, ins.IST, ins.ISF,
    ins.ISTYPE, ins.ISNUM,
    ins.MOV, ins.NOT, ins.UNM, ins.LEN,
    ins.ADDVN, ins.SUBVN, ins.MULVN, ins.DIVVN, ins.MODVN,
    ins.ADDNV, ins.SUBNV, ins.MULNV, ins.DIVNV, ins.MODNV,
    ins.ADDVV, ins.SUBVV, ins.MULVV, ins.DIVVV, ins.MODVV,
    ins.POW, ins.CAT,
    ins.KSTR, ins.KCDATA, ins.KSHORT, ins.KNUM, ins.KPRI, ins.KNIL,
    ins.UGET, ins.USETV, ins.USETS, ins.USETN, ins.USETP, ins.UCLO, ins.FNEW,
    ins.TNEW, ins.TDUP, ins.GGET, ins.GSET,
    ins.TGETV, ins.TGETS, ins.TGETB, ins.TGETR,
    ins.TSETV, ins.TSETS, ins.TSETB, ins.TSETM, ins.TSETR,
    ins.CALLM, ins.CALL, ins.CALLMT, ins.CALLT,
    ins.ITERC, ins.ITERN, ins.VARG, ins.ISNEXT,
    ins.RETM, ins.RET, ins.RET0, ins.RET1,
    ins.FORI, ins.JFORI, ins.FORL, ins.IFORL, ins.JFORL,
    ins.ITERL, ins.IITERL, ins.JITERL,
    ins.LOOP, ins.ILOOP, ins.JLOOP, ins.JMP,
    ins.FUNCF, ins.IFUNCF, ins.JFUNCF,
    ins.FUNCV, ins.IFUNCV, ins.JFUNCV,
    ins.FUNCC, ins.FUNCCW
]

none_instructions = []
for instruction in instruction_list:
    if instruction.opcode is None:
        none_instructions.append(instruction.name)
    else:
        print(f"{instruction.name}: {instruction.opcode}")

if none_instructions:
    print(f"\nInstructions with None opcode: {none_instructions}")
else:
    print("\nAll instructions have valid opcodes!")
