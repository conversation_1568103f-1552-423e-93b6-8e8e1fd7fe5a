#!/usr/bin/env python3

import re

def extract_bc_addresses(filename):
    """Extract BC_* function addresses from IDA Pro file"""
    bc_functions = {}
    
    with open(filename, 'r') as f:
        for line in f:
            # Look for BC_* function definitions with addresses
            if 'BC_' in line and 'LOAD:' in line:
                # Extract BC_ name and address
                bc_match = re.search(r'BC_([A-Z0-9]+)', line)
                addr_match = re.search(r'LOAD:([0-9A-F]+)', line)
                
                if bc_match and addr_match:
                    bc_name = bc_match.group(1)
                    address = addr_match.group(1)
                    
                    # Only keep the first occurrence (main function, not chunks)
                    if bc_name not in bc_functions:
                        bc_functions[bc_name] = address
    
    return bc_functions

def main():
    print("🔍 Creating LuaJIT Opcode Mapping")
    print("=" * 50)
    
    # Your provided opcode order
    opcode_order = [
        "ISLT", "ISGE", "ISLE", "ISGT",
        "ISEQV", "ISNEV", "ISEQS", "ISNES", 
        "ISEQN", "ISNEN", "ISEQP", "ISNEP",
        "ISFC", "ISTC", "ISF", "IST",
        "ISTYPE", "ISNUM", "NOT", "MOV", "LEN", "UNM",
        "ADDVN", "SUBVN", "MULVN", "DIVVN", "MODVN",
        "ADDNV", "SUBNV", "MULNV", "DIVNV", "MODNV",
        "ADDVV", "SUBVV", "MULVV", "DIVVV", "MODVV",
        "POW", "CAT",
        "UGET", "USETV", "USETS", "USETN", "USETP", "UCLO", "FNEW",
        "KSTR", "KCDATA", "KSHORT", "KNUM", "KPRI", "KNIL",
        "TNEW", "TDUP", "GGET", "GSET",
        "TGETV", "TGETS", "TGETB", "TGETR",
        "TSETV", "TSETS", "TSETB", "TSETM", "TSETR",
        "CALLM", "CALL", "CALLMT", "CALLT",
        "ITERC", "ITERN", "VARG", "ISNEXT",
        "RETM", "RET", "RET0", "RET1",
        "FORI", "JFORL", "FORL", "IFORL", "JFORL",
        "ITERL", "IITERL", "JITERL",
        "LOOP", "ILOOP", "JLOOP", "JMP",
        "FUNCF", "IFUNCF", "JFUNCF",
        "FUNCV", "IFUNCV", "JFUNCV",
        "FUNCC", "FUNCCW"
    ]
    
    # Extract addresses from IDA file
    print("📖 Extracting BC_* addresses from IDA Pro file...")
    bc_addresses = extract_bc_addresses('ida pro opcode.txt')
    print(f"   Found {len(bc_addresses)} BC_* functions")
    
    # Create mapping
    print("\n📝 Creating opcode mapping...")
    
    with open('opcode.txt', 'w') as f:
        f.write("# LuaJIT Opcode Mapping\n")
        f.write("# Based on modified LuaJIT with custom opcode order\n")
        f.write("# Format: 0xIndex: 0xAddress = BC_OPCODE_NAME\n\n")
        
        mapped_count = 0
        for i, opcode_name in enumerate(opcode_order):
            if opcode_name in bc_addresses:
                address = bc_addresses[opcode_name]
                line = f"0x{i:x}: 0x{address} = BC_{opcode_name}\n"
                f.write(line)
                print(f"  {line.strip()}")
                mapped_count += 1
            else:
                line = f"0x{i:x}: 0x???????? = BC_{opcode_name}  # NOT FOUND\n"
                f.write(line)
                print(f"  {line.strip()}")
    
    print(f"\n✅ Mapping complete!")
    print(f"📊 Successfully mapped: {mapped_count}/{len(opcode_order)} opcodes")
    print(f"💾 Saved to: opcode.txt")
    
    # Show missing opcodes
    missing = [op for op in opcode_order if op not in bc_addresses]
    if missing:
        print(f"\n⚠️  Missing opcodes: {missing}")
    
    # Show extra opcodes found in IDA but not in order
    extra = [op for op in bc_addresses if op not in opcode_order]
    if extra:
        print(f"\n📋 Extra opcodes found in IDA: {extra}")

if __name__ == "__main__":
    main()
