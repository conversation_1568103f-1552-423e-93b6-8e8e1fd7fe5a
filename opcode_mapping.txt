# LuaJIT Opcode Mapping
# Format: Index: Address = BC_OPCODE_NAME (confidence)

0x00: 0x0000000000BEBE94 = BC_ISNEV (1.00)
0x01: 0x0000000000BEBF14 = BC_ISEQS (1.00)
0x02: 0x0000000000BEBF64 = BC_ISNES (1.00)
0x03: 0x0000000000BEBFB4 = BC_ISEQN (1.00)
0x04: 0x0000000000BEC058 = BC_ISNEN (1.00)
0x05: 0x0000000000BEC10C = BC_ISEQP (1.00)
0x06: 0x0000000000BEC150 = BC_ISNEP (1.00)
0x07: 0x0000000000BEC190 = BC_ISFC (1.00)
0x08: 0x0000000000BEC1D0 = BC_ISTC (1.00)
0x09: 0x0000000000BEC208 = BC_ISF (1.00)
0x0a: 0x0000000000BEC240 = BC_IST (1.00)
0x0b: 0x0000000000BEC244 = BC_ISTYPE (1.00)
0x0c: 0x0000000000BEC268 = BC_ISNUM (1.00)
0x0d: 0x0000000000BEC28C = BC_NOT (1.00)
0x0e: 0x0000000000BEC2BC = BC_MOV (1.00)
0x0f: 0x0000000000BEC2DC = BC_LEN (1.00)
0x10: 0x0000000000BEC324 = BC_UNM (1.00)
0x11: 0x0000000000BEC368 = BC_ADDVN (1.00)
0x12: 0x0000000000BEC3D4 = BC_SUBVN (1.00)
0x13: 0x0000000000BEC440 = BC_MULVN (1.00)
0x14: 0x0000000000BEC4B4 = BC_DIVVN (1.00)
0x15: 0x0000000000BEC4FC = BC_MODVN (1.00)
0x16: 0x0000000000BEC570 = BC_ADDNV (1.00)
0x17: 0x0000000000BEC5DC = BC_SUBNV (1.00)
0x18: 0x0000000000BEC648 = BC_MULNV (1.00)
0x19: 0x0000000000BEC6BC = BC_DIVNV (1.00)
0x1a: 0x0000000000BEC704 = BC_MODNV (1.00)
0x1b: 0x0000000000BEC778 = BC_ADDVV (1.00)
0x1c: 0x0000000000BEC7E4 = BC_SUBVV (1.00)
0x1d: 0x0000000000BEC850 = BC_MULVV (1.00)
0x1e: 0x0000000000BEC8C4 = BC_DIVVV (1.00)
0x1f: 0x0000000000BEC90C = BC_MODVV (1.00)
0x20: 0x0000000000BEC980 = BC_POW (1.00)
0x21: 0x0000000000BEC9C8 = BC_CAT (1.00)
0x22: 0x0000000000BECA44 = BC_UGET (1.00)
0x23: 0x0000000000BECAB4 = BC_USETV (1.00)
0x24: 0x0000000000BECB1C = BC_USETS (1.00)
0x25: 0x0000000000BECB50 = BC_USETN (1.00)
0x26: 0x0000000000BECB84 = BC_USETP (1.00)
0x27: 0x0000000000BECBC0 = BC_UCLO (1.00)
0x28: 0x0000000000BECC08 = BC_FNEW (1.00)
0x29: 0x0000000000BECC34 = BC_KSTR (1.00)
0x2a: 0x0000000000BECC60 = BC_KCDATA (1.00)
0x2b: 0x0000000000BECC84 = BC_KSHORT (1.00)
0x2c: 0x0000000000BECCA4 = BC_KNUM (1.00)
0x2d: 0x0000000000BECCC4 = BC_KPRI (1.00)
0x2e: 0x0000000000BECCF4 = BC_KNIL (1.00)
0x2f: 0x0000000000BECDA8 = BC_TDUP (1.00)
0x30: 0x0000000000BECDC4 = BC_GSET (1.00)
0x31: 0x0000000000BECDDC = BC_TGETV (1.00)
0x32: 0x0000000000BECE68 = BC_TGETS (1.00)
0x33: 0x0000000000BED27C = BC_TSETM (1.00)
0x34: 0x0000000000BED2F8 = BC_CALL (1.00)
0x35: 0x0000000000BED34C = BC_CALLT (1.00)
0x36: 0x0000000000BED514 = BC_ITERN (1.00)
0x37: 0x0000000000BED5D0 = BC_VARG (1.00)
0x38: 0x0000000000BED670 = BC_ISNEXT (1.00)
0x39: 0x0000000000BED688 = BC_RET (1.00)
0x3a: 0x0000000000BED72C = BC_RETM (1.00)
0x3b: 0x0000000000BED794 = BC_RET0 (1.00)
0x3c: 0x0000000000BED804 = BC_RET1 (1.00)
0x3d: 0x0000000000BEDAAC = BC_ITERL (1.00)
0x3e: 0x0000000000BEDB10 = BC_LOOP (1.00)
0x3f: 0x0000000000BEDB14 = BC_IITERL (1.00)
0x40: 0x0000000000BEDB54 = BC_JMP (1.00)
0x41: 0x0000000000BEDBD4 = BC_JLOOP (1.00)
0x42: 0x0000000000BEDE48 = BC_FUNCCW (1.00)
0x43: 0x0000000000BEDE50 = BC_IFUNCF (1.00)
0x44: 0x0000000000BEDE98 = BC_FUNCF (1.00)
0x45: 0x0000000000BEE15C = BC_TGETB (1.00)
0x46: 0x0000000000BEE1D0 = BC_TGETR (1.00)
0x47: 0x0000000000BEE1E0 = BC_TSETS (1.00)
0x48: 0x0000000000BEE214 = BC_TSETV (1.00)
0x49: 0x0000000000BEE218 = BC_TSETB (1.00)
0x4a: 0x0000000000BEE284 = BC_TSETR (1.00)
0x4b: 0x0000000000BEE288 = BC_ISGE (1.00)
0x4c: 0x0000000000BEE288 = BC_ISLE (1.00)
0x4d: 0x0000000000BEE288 = BC_ISGT (1.00)
0x4e: 0x0000000000BEE344 = BC_ISEQV (1.00)
0x4f: 0x0000000000BEE3C4 = BC_ISLT (1.00)
0x50: 0x0000000000BEE3E0 = BC_CALLM (1.00)
0x51: 0x0000000000BEE3E0 = BC_ITERC (1.00)
0x52: 0x0000000000BEE44C = BC_CALLMT (1.00)
0x53: 0x0000000000BEE450 = BC_FORL (1.00)
0x54: 0x0000000000BEE450 = BC_IFORL (1.00)
