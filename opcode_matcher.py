#!/usr/bin/env python3

import re
import sys
from collections import defaultdict

def parse_ida_opcodes(filename):
    """Parse IDA Pro opcode.txt file to extract BC_* functions and their assembly"""
    opcodes = {}
    current_opcode = None
    current_address = None
    current_assembly = []
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            
            # Look for BC_* function definitions
            bc_match = re.search(r'BC_([A-Z0-9]+)\s*$', line)
            if bc_match:
                # Save previous opcode if exists
                if current_opcode and current_address:
                    opcodes[current_opcode] = {
                        'address': current_address,
                        'assembly': current_assembly.copy()
                    }
                
                current_opcode = bc_match.group(1)
                # Extract address from the line
                addr_match = re.search(r'LOAD:([0-9A-F]+)', line)
                if addr_match:
                    current_address = addr_match.group(1)
                current_assembly = []
                continue
            
            # Look for assembly instructions
            if current_opcode and 'LOAD:' in line:
                # Extract assembly instruction
                parts = line.split()
                if len(parts) >= 2:
                    # Find the instruction part (after address)
                    for i, part in enumerate(parts):
                        if part.startswith('LOAD:'):
                            if i + 1 < len(parts):
                                # Get instruction and operands
                                instruction_parts = parts[i+1:]
                                # Filter out comments and extra info
                                clean_parts = []
                                for p in instruction_parts:
                                    if p.startswith(';') or p.startswith('#'):
                                        break
                                    clean_parts.append(p)
                                if clean_parts:
                                    current_assembly.append(' '.join(clean_parts))
                            break
    
    # Save last opcode
    if current_opcode and current_address:
        opcodes[current_opcode] = {
            'address': current_address,
            'assembly': current_assembly.copy()
        }
    
    return opcodes

def parse_vm_source(filename):
    """Parse vm_arm64.dasc to extract BC_* case implementations"""
    source_opcodes = {}
    current_cases = []
    current_assembly = []
    in_case = False
    
    with open(filename, 'r') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for case BC_* statements
        case_match = re.search(r'case\s+BC_([A-Z0-9_]+)', line)
        if case_match:
            # Save previous case if exists
            if current_cases and current_assembly:
                for case_name in current_cases:
                    source_opcodes[case_name] = current_assembly.copy()
            
            # Parse all BC_ cases on this line
            current_cases = []
            all_cases = re.findall(r'BC_([A-Z0-9_]+)', line)
            current_cases.extend(all_cases)
            current_assembly = []
            in_case = True
            i += 1
            continue
        
        if in_case:
            # Look for break; to end the case
            if 'break;' in line:
                # Save current case
                if current_cases and current_assembly:
                    for case_name in current_cases:
                        source_opcodes[case_name] = current_assembly.copy()
                current_cases = []
                current_assembly = []
                in_case = False
            else:
                # Extract assembly instructions (lines starting with |)
                if line.startswith('|'):
                    # Clean up the assembly line
                    asm_line = line[1:].strip()
                    # Remove comments
                    if '//' in asm_line:
                        asm_line = asm_line.split('//')[0].strip()
                    if asm_line and not asm_line.startswith('.'):
                        current_assembly.append(asm_line)
        
        i += 1
    
    # Save last case
    if current_cases and current_assembly:
        for case_name in current_cases:
            source_opcodes[case_name] = current_assembly.copy()
    
    return source_opcodes

def normalize_instruction(instruction):
    """Normalize assembly instruction for comparison"""
    # Remove extra whitespace and convert to lowercase
    instruction = re.sub(r'\s+', ' ', instruction.strip().lower())
    
    # Normalize register names (x19 -> BASE, etc.)
    register_map = {
        'x19': 'base', 'x20': 'kbase', 'x21': 'pc', 'x22': 'glreg',
        'x23': 'lreg', 'x24': 'tisnum', 'x25': 'tisnumhi', 'x26': 'tisnil',
        'x27': 'ra', 'x28': 'rc', 'x17': 'rb',
        'w27': 'raw', 'w28': 'rcw', 'w17': 'rbw', 'x16': 'ins'
    }
    
    for reg, alias in register_map.items():
        instruction = instruction.replace(reg, alias)
    
    return instruction

def calculate_similarity(ida_asm, source_asm):
    """Calculate similarity between IDA assembly and source assembly"""
    if not ida_asm or not source_asm:
        return 0.0
    
    # Normalize both instruction sequences
    ida_normalized = [normalize_instruction(inst) for inst in ida_asm]
    source_normalized = [normalize_instruction(inst) for inst in source_asm]
    
    # Simple similarity: count matching instructions
    matches = 0
    total = max(len(ida_normalized), len(source_normalized))
    
    for i in range(min(len(ida_normalized), len(source_normalized))):
        if ida_normalized[i] == source_normalized[i]:
            matches += 1
    
    return matches / total if total > 0 else 0.0

def main():
    print("🔍 LuaJIT Opcode Matcher")
    print("=" * 50)

    # Parse input files
    print("📖 Parsing IDA Pro opcodes...")
    ida_opcodes = parse_ida_opcodes('ida pro opcode.txt')
    print(f"   Found {len(ida_opcodes)} BC_* functions in IDA")

    print("📖 Parsing VM source code...")
    source_opcodes = parse_vm_source('vm_arm64.dasc')
    print(f"   Found {len(source_opcodes)} BC_* cases in source")

    # Debug: show some examples
    print("\n🔍 Debug: IDA opcodes sample:")
    for i, (name, data) in enumerate(list(ida_opcodes.items())[:3]):
        print(f"  {name}: {data['address']} - {len(data['assembly'])} instructions")
        if data['assembly']:
            print(f"    First: {data['assembly'][0]}")

    print("\n🔍 Debug: Source opcodes sample:")
    for i, (name, asm) in enumerate(list(source_opcodes.items())[:3]):
        print(f"  {name}: {len(asm)} instructions")
        if asm:
            print(f"    First: {asm[0]}")

    # Try exact name matching first
    print("\n🔄 Trying exact name matching...")
    exact_matches = {}
    for ida_name in ida_opcodes:
        if ida_name in source_opcodes:
            exact_matches[ida_name] = {
                'source_name': ida_name,
                'address': ida_opcodes[ida_name]['address'],
                'score': 1.0
            }

    print(f"   Found {len(exact_matches)} exact name matches")

    # Match opcodes by similarity
    print("\n🔄 Matching by assembly similarity...")
    similarity_matches = {}

    for ida_name, ida_data in ida_opcodes.items():
        if ida_name in exact_matches:
            continue  # Skip already matched

        best_match = None
        best_score = 0.0

        for source_name, source_asm in source_opcodes.items():
            if source_name in [m['source_name'] for m in exact_matches.values()]:
                continue  # Skip already used source names

            score = calculate_similarity(ida_data['assembly'], source_asm)
            if score > best_score:
                best_score = score
                best_match = source_name

        if best_match and best_score > 0.1:  # Lower threshold
            similarity_matches[ida_name] = {
                'source_name': best_match,
                'address': ida_data['address'],
                'score': best_score
            }

    # Combine matches
    all_matches = {**exact_matches, **similarity_matches}

    # Sort by address for sequential output
    sorted_matches = sorted(all_matches.items(), key=lambda x: x[1]['address'])

    # Generate output
    print(f"\n✅ Found {len(sorted_matches)} total matches")
    print(f"   - Exact matches: {len(exact_matches)}")
    print(f"   - Similarity matches: {len(similarity_matches)}")

    print("\n📝 Generating opcode mapping...")

    with open('opcode_mapping.txt', 'w') as f:
        f.write("# LuaJIT Opcode Mapping\n")
        f.write("# Format: Index: Address = BC_OPCODE_NAME (confidence)\n\n")

        for i, (ida_name, match_data) in enumerate(sorted_matches):
            line = f"0x{i:02x}: 0x{match_data['address']} = BC_{match_data['source_name']} ({match_data['score']:.2f})\n"
            f.write(line)
            print(f"  {line.strip()}")

    print(f"\n💾 Mapping saved to opcode_mapping.txt")
    print(f"📊 Total opcodes mapped: {len(sorted_matches)}")

if __name__ == "__main__":
    main()
