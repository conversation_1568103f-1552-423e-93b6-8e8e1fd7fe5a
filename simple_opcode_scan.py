#!/usr/bin/env python3

import struct

def analyze_luajit_opcodes(filename):
    """Analyze LuaJIT bytecode file to find actual opcodes used"""
    with open(filename, 'rb') as f:
        data = f.read()

    print(f"=== LuaJIT Bytecode Analysis ===")
    print(f"File: {filename}")
    print(f"Size: {len(data)} bytes")

    # Check LuaJIT header
    if len(data) < 5:
        print("File too small to be LuaJIT bytecode")
        return

    magic = data[:3]
    version = data[3]
    flags = data[4]

    print(f"Magic: {magic.hex()} ({''.join(chr(b) if 32 <= b <= 126 else '.' for b in magic)})")
    print(f"Version: 0x{version:02x} ({version})")
    print(f"Flags: 0x{flags:02x} ({flags})")

    if magic != b'\x1bLJ':
        print("Warning: Not a standard LuaJIT file!")

    # Find bytecode sections by looking for instruction patterns
    opcodes_found = set()
    instruction_positions = []

    # LuaJIT instructions are 4 bytes: opcode + 3 bytes of operands
    # Look for patterns that suggest bytecode instructions
    i = 0
    while i < len(data) - 3:
        # Skip obvious string data (printable ASCII sequences)
        if (32 <= data[i] <= 126 and
            i + 1 < len(data) and 32 <= data[i+1] <= 126 and
            i + 2 < len(data) and 32 <= data[i+2] <= 126):
            i += 1
            continue

        # Check if this could be an instruction
        opcode = data[i]

        # Reasonable opcode range (0-255, but typically much smaller)
        if opcode <= 255:
            # Check if the next 3 bytes look like reasonable operands
            if i + 3 < len(data):
                operand1 = data[i+1]
                operand2 = data[i+2]
                operand3 = data[i+3]

                # Heuristic: if we're in a section with multiple potential opcodes
                # and the pattern looks instruction-like, count it
                looks_like_instruction = False

                # Pattern 1: Low opcode values are more likely
                if opcode < 200:
                    looks_like_instruction = True

                # Pattern 2: Check surrounding context for instruction-like patterns
                if i >= 4:
                    prev_could_be_opcode = data[i-4] < 200
                    if prev_could_be_opcode:
                        looks_like_instruction = True

                # Pattern 3: Look for common LuaJIT instruction patterns
                if opcode in range(0x40, 0x70):  # Common range for many opcodes
                    looks_like_instruction = True

                if looks_like_instruction:
                    opcodes_found.add(opcode)
                    instruction_positions.append(i)

        i += 1

    print(f"\n=== Bytecode Analysis Results ===")
    print(f"Potential instructions found: {len(instruction_positions)}")
    print(f"Unique opcodes detected: {len(opcodes_found)}")
    print(f"Opcode range: 0x{min(opcodes_found):02x} - 0x{max(opcodes_found):02x} ({min(opcodes_found)} - {max(opcodes_found)})")

    print(f"\n=== All Opcodes Found ===")
    opcodes_sorted = sorted(opcodes_found)

    # Print in rows of 8 for better readability
    for i in range(0, len(opcodes_sorted), 8):
        row = opcodes_sorted[i:i+8]
        hex_row = [f"0x{op:02x}" for op in row]
        dec_row = [f"({op:3d})" for op in row]
        print("  " + "  ".join(f"{h:>4}" for h in hex_row))
        print("  " + "  ".join(f"{d:>4}" for d in dec_row))
        print()

    # Analyze opcode distribution
    print(f"=== Opcode Distribution Analysis ===")
    standard_luajit_max = 0x60  # 96 opcodes (0x00-0x60)

    standard_opcodes = [op for op in opcodes_found if op <= standard_luajit_max]
    extended_opcodes = [op for op in opcodes_found if op > standard_luajit_max]

    print(f"Standard LuaJIT opcodes (0x00-0x{standard_luajit_max:02x}): {len(standard_opcodes)}")
    print(f"Extended opcodes (>{standard_luajit_max:02x}): {len(extended_opcodes)}")

    if extended_opcodes:
        print(f"Extended opcodes: {[f'0x{op:02x}' for op in sorted(extended_opcodes)]}")

    # Check for specific problematic opcodes
    problematic = [0x68, 0x69, 0x6c, 0x6d, 0x6e, 0x6f]
    found_problematic = [op for op in problematic if op in opcodes_found]
    if found_problematic:
        print(f"Problematic opcodes found: {[f'0x{op:02x}' for op in found_problematic]}")

    return opcodes_found

if __name__ == "__main__":
    import sys
    filename = sys.argv[1] if len(sys.argv) > 1 else "modded.luac"
    analyze_luajit_opcodes(filename)
