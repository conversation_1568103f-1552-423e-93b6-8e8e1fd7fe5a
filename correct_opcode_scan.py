#!/usr/bin/env python3

import struct

def parse_luajit_bytecode(filename):
    """Correctly parse LuaJIT bytecode to find only real opcodes"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"=== Correct LuaJIT Opcode Analysis ===")
    print(f"File: {filename}")
    print(f"Size: {len(data)} bytes")
    
    if len(data) < 5:
        print("File too small")
        return
    
    # Parse LuaJIT header
    magic = data[:3]
    version = data[3]
    flags = data[4]
    
    print(f"Magic: {magic.hex()} ({''.join(chr(b) if 32 <= b <= 126 else '.' for b in magic)})")
    print(f"Version: 0x{version:02x}")
    print(f"Flags: 0x{flags:02x}")
    
    if magic != b'\x1bLJ':
        print("Not a LuaJIT file!")
        return
    
    # Skip header and find bytecode sections
    pos = 5
    opcodes_found = set()
    
    while pos < len(data):
        # Look for prototype header
        if pos + 4 >= len(data):
            break
            
        # Read potential prototype info
        try:
            # Skip to instruction section
            # LuaJIT format: header + constants + instructions
            
            # Simple heuristic: look for 4-byte aligned instruction patterns
            # Instructions are typically in blocks
            if pos % 4 == 1:  # Instructions start after size info
                # Read potential instruction
                if pos + 3 < len(data):
                    opcode = data[pos]
                    
                    # Valid opcode range for LuaJIT (0-96 for standard)
                    if opcode <= 96:  # Standard LuaJIT range
                        # Check if this looks like a real instruction
                        # by examining the pattern
                        operand1 = data[pos + 1]
                        operand2 = data[pos + 2] 
                        operand3 = data[pos + 3]
                        
                        # Instructions typically have reasonable operand values
                        # and appear in sequences
                        opcodes_found.add(opcode)
            
            pos += 1
            
        except (struct.error, IndexError):
            pos += 1
    
    print(f"\n=== Real Opcodes Found ===")
    print(f"Total unique opcodes: {len(opcodes_found)}")
    
    if opcodes_found:
        opcodes_sorted = sorted(opcodes_found)
        print(f"Range: 0x{min(opcodes_sorted):02x} - 0x{max(opcodes_sorted):02x}")
        
        # Print in rows
        for i in range(0, len(opcodes_sorted), 8):
            row = opcodes_sorted[i:i+8]
            hex_row = [f"0x{op:02x}" for op in row]
            print("  " + "  ".join(f"{h:>4}" for h in hex_row))
    
    return opcodes_found

def parse_luajit_properly(filename):
    """Use a more systematic approach to parse LuaJIT bytecode"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"\n=== Systematic LuaJIT Analysis ===")
    print(f"File: {filename}")
    
    # Parse header
    if len(data) < 5 or data[:3] != b'\x1bLJ':
        print("Invalid LuaJIT file")
        return set()
    
    pos = 5  # Skip header
    opcodes = set()
    
    # Look for prototype sections
    while pos < len(data) - 4:
        # Try to find instruction sequences
        # Instructions are 4 bytes: opcode + 3 operand bytes
        
        # Check if we're at start of an instruction block
        potential_opcode = data[pos]
        
        # Standard LuaJIT opcodes are 0-96
        if potential_opcode <= 96:
            # Look ahead to see if this is part of an instruction sequence
            valid_sequence = True
            sequence_length = 0
            
            # Check next few instructions
            for i in range(0, min(20, (len(data) - pos) // 4)):  # Check up to 5 instructions
                check_pos = pos + i * 4
                if check_pos + 3 >= len(data):
                    break
                    
                check_opcode = data[check_pos]
                if check_opcode > 96:  # Invalid opcode
                    if i == 0:  # First instruction is invalid
                        valid_sequence = False
                        break
                    else:  # End of instruction sequence
                        break
                else:
                    sequence_length += 1
            
            # If we found a valid sequence of at least 2 instructions
            if valid_sequence and sequence_length >= 2:
                # Add all opcodes in this sequence
                for i in range(sequence_length):
                    inst_pos = pos + i * 4
                    if inst_pos < len(data):
                        opcodes.add(data[inst_pos])
                
                # Skip this instruction block
                pos += sequence_length * 4
                continue
        
        pos += 1
    
    print(f"Found {len(opcodes)} real opcodes")
    if opcodes:
        sorted_opcodes = sorted(opcodes)
        print(f"Range: 0x{min(sorted_opcodes):02x} - 0x{max(sorted_opcodes):02x}")
        
        # Print opcodes
        for i in range(0, len(sorted_opcodes), 8):
            row = sorted_opcodes[i:i+8]
            hex_row = [f"0x{op:02x}" for op in row]
            print("  " + "  ".join(f"{h:>4}" for h in hex_row))
    
    return opcodes

if __name__ == "__main__":
    import sys
    filename = sys.argv[1] if len(sys.argv) > 1 else "test_features.luac"
    
    # Try both methods
    opcodes1 = parse_luajit_bytecode(filename)
    opcodes2 = parse_luajit_properly(filename)
    
    print(f"\n=== Comparison ===")
    print(f"Method 1 found: {len(opcodes1)} opcodes")
    print(f"Method 2 found: {len(opcodes2)} opcodes")
    
    if opcodes2:
        print(f"Method 2 range: 0x{min(opcodes2):02x} - 0x{max(opcodes2):02x}")
        if max(opcodes2) <= 96:
            print("✅ This looks like standard LuaJIT (opcodes ≤ 96)")
        else:
            print("⚠️  Extended opcodes detected")
