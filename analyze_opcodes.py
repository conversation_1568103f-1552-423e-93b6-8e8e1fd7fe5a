#!/usr/bin/env python3

import ljd
import ljd.rawdump.parser
import ljd.rawdump.code

# Initialize LuaJIT version
ljd.CURRENT_VERSION = 2.1
from ljd.rawdump.luajit.v2_1.luajit_opcode import _OPCODES as opcodes
ljd.rawdump.code.init(opcodes)

def analyze_file(filename):
    print(f"Analyzing {filename}...")

    try:
        def on_parse_header(header):
            print(f"Header version: {header.version}")
            print(f"Header flags: {header.flags}")

        header, prototype = ljd.rawdump.parser.parse(filename, on_parse_header)

        if prototype:
            opcodes_found = set()
            analyze_prototype(prototype, opcodes_found)

            print(f"\nFound {len(opcodes_found)} unique opcodes:")
            for opcode in sorted(opcodes_found):
                print(f"  0x{opcode:02x} ({opcode})")

            # Check which opcodes are unknown
            unknown_opcodes = []
            for opcode in opcodes_found:
                if opcode >= 97:  # Our defined opcodes are 0-96
                    unknown_opcodes.append(opcode)

            if unknown_opcodes:
                print(f"\nUnknown opcodes (>= 97): {unknown_opcodes}")
            else:
                print("\nAll opcodes are within expected range")

    except Exception as e:
        print(f"Error analyzing file: {e}")
        import traceback
        traceback.print_exc()

def analyze_prototype(prototype, opcodes_found):
    """Recursively analyze a prototype and its children for opcodes"""
    if hasattr(prototype, 'instructions') and prototype.instructions:
        for instruction in prototype.instructions:
            if hasattr(instruction, 'opcode'):
                opcodes_found.add(instruction.opcode)
    
    # Analyze child prototypes
    if hasattr(prototype, 'constants') and prototype.constants:
        for constant in prototype.constants:
            if hasattr(constant, 'type') and constant.type == ljd.rawdump.constants.T_FUNCTION:
                if hasattr(constant, 'value'):
                    analyze_prototype(constant.value, opcodes_found)

if __name__ == "__main__":
    analyze_file("modded.luac")
