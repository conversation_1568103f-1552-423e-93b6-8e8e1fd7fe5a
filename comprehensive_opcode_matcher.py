#!/usr/bin/env python3

import re
import sys
from collections import defaultdict, OrderedDict

def parse_ida_functions(filename):
    """Parse IDA Pro file to extract BC_* functions with their assembly instructions"""
    functions = {}
    current_function = None
    current_address = None
    current_instructions = []
    
    with open(filename, 'r') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for BC_* function definitions
        if 'BC_' in line and 'LOAD:' in line and ('__fastcall' in line or line.endswith('BC_' + re.search(r'BC_([A-Z0-9]+)', line).group(1) if re.search(r'BC_([A-Z0-9]+)', line) else '')):
            bc_match = re.search(r'BC_([A-Z0-9]+)', line)
            addr_match = re.search(r'LOAD:([0-9A-F]+)', line)
            
            if bc_match and addr_match:
                # Save previous function
                if current_function and current_address:
                    functions[current_function] = {
                        'address': current_address,
                        'instructions': current_instructions.copy()
                    }
                
                current_function = bc_match.group(1)
                current_address = addr_match.group(1)
                current_instructions = []
                
        # Extract assembly instructions for current function
        elif current_function and 'LOAD:' in line:
            # Parse assembly instruction
            parts = line.split()
            if len(parts) >= 2:
                # Find instruction after address
                for j, part in enumerate(parts):
                    if part.startswith('LOAD:') and j + 1 < len(parts):
                        # Get instruction and operands
                        instruction_parts = parts[j+1:]
                        # Remove comments and extra info
                        clean_parts = []
                        for p in instruction_parts:
                            if p.startswith(';') or p.startswith('#') or p.startswith('//'):
                                break
                            clean_parts.append(p)
                        
                        if clean_parts:
                            instruction = ' '.join(clean_parts)
                            # Filter out non-instruction lines
                            if not any(x in instruction.lower() for x in ['end of function', 'function chunk', 'code xref', 'data xref']):
                                current_instructions.append(instruction)
                        break
        
        # Check for end of function
        elif current_function and ('End of function' in line or 'START OF FUNCTION CHUNK' in line):
            if current_function and current_address:
                functions[current_function] = {
                    'address': current_address,
                    'instructions': current_instructions.copy()
                }
            current_function = None
            current_address = None
            current_instructions = []
        
        i += 1
    
    # Save last function
    if current_function and current_address:
        functions[current_function] = {
            'address': current_address,
            'instructions': current_instructions.copy()
        }
    
    return functions

def parse_vm_source_patterns(filename):
    """Parse vm_arm64.dasc to extract assembly patterns for each BC_* case"""
    patterns = {}
    current_cases = []
    current_pattern = []
    in_case = False
    
    with open(filename, 'r') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for case BC_* statements
        case_match = re.search(r'case\s+BC_([A-Z0-9_]+)', line)
        if case_match:
            # Save previous case
            if current_cases and current_pattern:
                for case_name in current_cases:
                    patterns[case_name] = current_pattern.copy()
            
            # Extract all BC_ cases from this line
            current_cases = re.findall(r'BC_([A-Z0-9_]+)', line)
            current_pattern = []
            in_case = True
            
        elif in_case:
            if 'break;' in line:
                # End of case - save pattern
                if current_cases and current_pattern:
                    for case_name in current_cases:
                        patterns[case_name] = current_pattern.copy()
                current_cases = []
                current_pattern = []
                in_case = False
            else:
                # Extract assembly instructions (lines starting with |)
                if line.startswith('|') and not line.startswith('|.'):
                    asm_line = line[1:].strip()
                    # Remove comments
                    if '//' in asm_line:
                        asm_line = asm_line.split('//')[0].strip()
                    if asm_line and not asm_line.startswith('.') and not asm_line.startswith('->'):
                        current_pattern.append(asm_line)
        
        i += 1
    
    return patterns

def normalize_instruction(instruction):
    """Normalize assembly instruction for comparison"""
    # Convert to uppercase and normalize whitespace
    instruction = re.sub(r'\s+', ' ', instruction.strip().upper())
    
    # Normalize common patterns
    instruction = re.sub(r'#0X([0-9A-F]+)', r'#\1', instruction)  # Remove 0x prefix
    instruction = re.sub(r'W(\d+)', r'X\1', instruction)  # Normalize register width references
    
    return instruction

def calculate_exact_match_score(ida_instructions, source_pattern):
    """Calculate exact match score between IDA instructions and source pattern"""
    if not ida_instructions or not source_pattern:
        return 0.0
    
    # Normalize both sequences
    ida_norm = [normalize_instruction(inst) for inst in ida_instructions[:10]]  # First 10 instructions
    source_norm = [normalize_instruction(inst) for inst in source_pattern[:10]]
    
    # Count exact matches
    matches = 0
    min_len = min(len(ida_norm), len(source_norm))
    
    for i in range(min_len):
        if ida_norm[i] == source_norm[i]:
            matches += 1
    
    # Calculate score
    if min_len == 0:
        return 0.0
    
    return matches / min_len

def main():
    print("🔍 Comprehensive LuaJIT Opcode Matcher")
    print("=" * 60)
    
    # Parse IDA Pro functions
    print("📖 Parsing IDA Pro BC_* functions...")
    ida_functions = parse_ida_functions('ida pro opcode.txt')
    print(f"   Found {len(ida_functions)} BC_* functions")
    
    # Parse VM source patterns
    print("📖 Parsing VM source patterns...")
    source_patterns = parse_vm_source_patterns('vm_arm64.dasc')
    print(f"   Found {len(source_patterns)} BC_* patterns")
    
    # Debug output
    print(f"\n🔍 IDA functions found: {sorted(ida_functions.keys())}")
    print(f"🔍 Source patterns found: {sorted(source_patterns.keys())}")
    
    # Match functions to patterns
    print("\n🔄 Matching functions to patterns...")
    matches = {}
    
    for ida_name, ida_data in ida_functions.items():
        best_match = None
        best_score = 0.0
        
        for source_name, source_pattern in source_patterns.items():
            score = calculate_exact_match_score(ida_data['instructions'], source_pattern)
            if score > best_score:
                best_score = score
                best_match = source_name
        
        if best_match and best_score > 0.5:  # Require at least 50% match
            matches[ida_name] = {
                'source_name': best_match,
                'address': ida_data['address'],
                'score': best_score
            }
            print(f"  {ida_name} -> {best_match} (score: {best_score:.2f})")
    
    # Sort by address for sequential mapping
    sorted_matches = sorted(matches.items(), key=lambda x: int(x[1]['address'], 16))
    
    # Generate final mapping
    print(f"\n📝 Generating final opcode mapping...")
    
    with open('opcode.txt', 'w') as f:
        f.write("# LuaJIT Opcode Mapping\n")
        f.write("# Based on modified LuaJIT with exact assembly matching\n")
        f.write("# Format: 0xIndex: 0xAddress = BC_OPCODE_NAME\n\n")
        
        for i, (ida_name, match_data) in enumerate(sorted_matches):
            line = f"0x{i:x}: 0x{match_data['address']} = BC_{match_data['source_name']}\n"
            f.write(line)
            print(f"  {line.strip()}")
    
    print(f"\n✅ Mapping complete!")
    print(f"📊 Successfully mapped: {len(sorted_matches)} opcodes")
    print(f"💾 Saved to: opcode.txt")

if __name__ == "__main__":
    main()
